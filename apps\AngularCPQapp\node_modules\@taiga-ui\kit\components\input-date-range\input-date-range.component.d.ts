import { AfterViewChecked, ChangeDetectorRef, Type } from '@angular/core';
import { NgControl } from '@angular/forms';
import { MaskitoOptions } from '@maskito/core';
import { AbstractTuiNullableControl, AbstractTuiValueTransformer, TuiBooleanHandler, TuiDateMode, TuiDay, TuiDayLike, TuiDayRange, TuiFocusableElementAccessor, TuiMonth, TuiTypedMapper } from '@taiga-ui/cdk';
import { TuiMarkerHandler, TuiSizeL, TuiSizeS, TuiTextfieldSizeDirective, TuiWithOptionalMinMax } from '@taiga-ui/core';
import { TuiDayRangePeriod } from '@taiga-ui/kit/classes';
import { TuiInputDateOptions } from '@taiga-ui/kit/tokens';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiInputDateRangeComponent extends AbstractTuiNullableControl<TuiDayRange> implements TuiWithOptionalMinMax<TuiDay>, TuiFocusableElementAccessor, AfterViewChecked {
    private readonly isMobile;
    private readonly mobileCalendar;
    readonly dateFormat: TuiDateMode;
    readonly dateSeparator: string;
    readonly dateTexts$: Observable<Record<TuiDateMode, string>>;
    readonly valueTransformer: AbstractTuiValueTransformer<TuiDayRange | null> | null;
    private readonly options;
    private readonly textfieldSize;
    private readonly textfield?;
    private readonly calendarRange?;
    disabledItemHandler: TuiBooleanHandler<TuiDay>;
    markerHandler: TuiMarkerHandler;
    defaultViewedMonth: TuiMonth;
    items: readonly TuiDayRangePeriod[];
    min: TuiDay | null;
    max: TuiDay | null;
    minLength: TuiDayLike | null;
    maxLength: TuiDayLike | null;
    open: boolean;
    selectedActivePeriod: TuiDayRangePeriod | null;
    readonly maxLengthMapper: TuiTypedMapper<[
        TuiDay,
        TuiDayRange | null,
        TuiDayLike | null,
        boolean
    ], TuiDay>;
    readonly dateFiller$: Observable<string>;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, isMobile: boolean, mobileCalendar: Type<Record<string, any>> | null, dateFormat: TuiDateMode, dateSeparator: string, dateTexts$: Observable<Record<TuiDateMode, string>>, valueTransformer: AbstractTuiValueTransformer<TuiDayRange | null> | null, options: TuiInputDateOptions, textfieldSize: TuiTextfieldSizeDirective);
    onClick(): void;
    ngAfterViewChecked(): void;
    get size(): TuiSizeL | TuiSizeS;
    get computedMin(): TuiDay;
    get computedMax(): TuiDay;
    get nativeFocusableElement(): HTMLInputElement | null;
    get focused(): boolean;
    get computedMobile(): boolean;
    get calendarIcon(): TuiInputDateOptions['icon'];
    get computedExampleText(): string;
    get computedMask(): MaskitoOptions;
    get activePeriod(): TuiDayRangePeriod | null;
    get computedValue(): string;
    get showValueTemplate(): boolean;
    get computedContent(): PolymorpheusContent;
    get innerPseudoFocused(): boolean | null;
    get nativeValue(): string;
    set nativeValue(value: string);
    getComputedRangeFiller(dateFiller: string): string;
    onIconClick(): void;
    onOpenChange(open: boolean): void;
    onValueChange(value: string): void;
    onRangeChange(range: TuiDayRange | null): void;
    onItemSelect(item: TuiDayRangePeriod | string): void;
    onActiveZone(focused: boolean): void;
    writeValue(value: TuiDayRange | null): void;
    protected valueIdenticalComparator(oldValue: TuiDayRange | null, newValue: TuiDayRange | null): boolean;
    private calculateMask;
    private get itemSelected();
    private toggle;
    private focusInput;
    private getDateRangeFiller;
    private findActivePeriodBy;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateRangeComponent, [{ optional: true; self: true; }, null, null, { optional: true; }, null, null, null, { optional: true; }, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputDateRangeComponent, "tui-input-date-range", never, { "disabledItemHandler": "disabledItemHandler"; "markerHandler": "markerHandler"; "defaultViewedMonth": "defaultViewedMonth"; "items": "items"; "min": "min"; "max": "max"; "minLength": "minLength"; "maxLength": "maxLength"; }, {}, never, ["*", "input"]>;
}
