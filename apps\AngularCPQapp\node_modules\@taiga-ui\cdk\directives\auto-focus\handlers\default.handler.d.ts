import { ElementRef } from '@angular/core';
import { TuiFocusableElementAccessor } from '@taiga-ui/cdk/interfaces';
import { Observable } from 'rxjs';
import { AbstractTuiAutofocusHandler } from './abstract.handler';
import * as i0 from "@angular/core";
export declare class TuiDefaultAutofocusHandler extends AbstractTuiAutofocusHandler {
    private readonly animationFrame$;
    constructor(focusable: TuiFocusableElementAccessor | null, el: ElementRef<HTMLElement>, animationFrame$: Observable<number>);
    setFocus(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiDefaultAutofocusHandler, [{ optional: true; self: true; }, null, null]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiDefaultAutofocusHandler, never, never, {}, {}, never>;
}
