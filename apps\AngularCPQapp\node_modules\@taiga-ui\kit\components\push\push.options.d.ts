import { Provider } from '@angular/core';
export interface TuiPushOptions {
    readonly buttons: readonly string[];
    readonly heading: string;
    readonly icon: string;
    readonly iconColor: string;
    readonly image: string;
    readonly timestamp: number | string;
    readonly type: string;
}
export declare const TUI_PUSH_DEFAULT_OPTIONS: TuiPushOptions;
/**
 * Default parameters for Push component
 */
export declare const TUI_PUSH_OPTIONS: import("@angular/core").InjectionToken<TuiPushOptions>;
export declare function tuiPushOptionsProvider(options: Partial<TuiPushOptions>): Provider;
