import { ElementRef } from '@angular/core';
import { TuiFocusVisibleService } from '@taiga-ui/cdk';
import { TuiCommonIcons } from '@taiga-ui/core';
import { Observable } from 'rxjs';
import { TuiStepperComponent } from '../stepper.component';
import * as i0 from "@angular/core";
export declare class TuiStepComponent {
    private readonly stepper;
    private readonly el;
    readonly icons: TuiCommonIcons;
    stepState: 'error' | 'normal' | 'pass';
    icon: string;
    focusVisible: boolean;
    constructor(focusVisible$: TuiFocusVisibleService, routerLinkActive$: Observable<boolean>, stepper: TuiStepperComponent, el: ElementRef<HTMLElement>, icons: TuiCommonIcons);
    get isActive(): boolean;
    get isVertical(): boolean;
    get tabIndex(): number;
    get index(): number;
    activate(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiStepComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiStepComponent, "button[tuiStep], a[tuiStep]:not([routerLink]), a[tuiStep][routerLink][routerLinkActive]", never, { "stepState": "stepState"; "icon": "icon"; }, {}, never, ["*"]>;
}
