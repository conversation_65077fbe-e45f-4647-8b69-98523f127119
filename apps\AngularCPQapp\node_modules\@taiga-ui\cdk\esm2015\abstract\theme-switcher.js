import { DOCUMENT } from '@angular/common';
import { Directive, Inject } from '@angular/core';
import * as i0 from "@angular/core";
/**
 * Use this abstract class to create your own toggleable themes.
 * A component extending this class must have CSS variables definitions
 * and have ViewEncapsulation set to NONE. A boolean input allows to
 * switch theme on or off.
 */
export class AbstractTuiThemeSwitcher {
    constructor(doc) {
        this.doc = doc;
        if (this.style !== null) {
            this.addTheme();
            return;
        }
        const styles = this.doc.head.querySelectorAll('style');
        this.constructor.style =
            styles[styles.length - 1];
    }
    get style() {
        return this.constructor.style;
    }
    ngOnDestroy() {
        this.removeTheme();
    }
    addTheme() {
        if (this.style) {
            this.doc.head.appendChild(this.style);
        }
    }
    removeTheme() {
        var _a;
        (_a = this.style) === null || _a === void 0 ? void 0 : _a.remove();
    }
}
AbstractTuiThemeSwitcher.style = null;
AbstractTuiThemeSwitcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: AbstractTuiThemeSwitcher, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive });
AbstractTuiThemeSwitcher.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: "12.0.0", version: "12.2.17", type: AbstractTuiThemeSwitcher, ngImport: i0 });
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: AbstractTuiThemeSwitcher, decorators: [{
            type: Directive
        }], ctorParameters: function () { return [{ type: Document, decorators: [{
                    type: Inject,
                    args: [DOCUMENT]
                }] }]; } });
//# sourceMappingURL=data:application/json;base64,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