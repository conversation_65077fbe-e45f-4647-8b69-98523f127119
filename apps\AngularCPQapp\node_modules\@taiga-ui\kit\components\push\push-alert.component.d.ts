import { AnimationOptions } from '@angular/animations';
import { TuiDialog } from '@taiga-ui/cdk';
import { TuiPushOptions } from './push.options';
import * as i0 from "@angular/core";
export declare class TuiPushAlertComponent {
    readonly animation: AnimationOptions;
    readonly context: TuiDialog<TuiPushOptions, string>;
    constructor(animation: AnimationOptions, context: TuiDialog<TuiPushOptions, string>);
    get isDirective(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPushAlertComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiPushAlertComponent, "tui-push-alert", never, {}, {}, never, never>;
}
