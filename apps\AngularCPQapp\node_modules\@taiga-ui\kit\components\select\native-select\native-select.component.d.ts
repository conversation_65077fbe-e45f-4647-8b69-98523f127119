import { AbstractTuiNativeSelect } from '@taiga-ui/kit/abstract';
import { TuiItemsHandlers } from '@taiga-ui/kit/tokens';
import type { TuiSelectDirective } from '../select.directive';
import * as i0 from "@angular/core";
export declare class TuiNativeSelectComponent<T> extends AbstractTuiNativeSelect<TuiSelectDirective, T> {
    items: readonly T[] | null;
    get stringify(): TuiItemsHandlers<T>['stringify'];
    selected(option: T): boolean;
    onValueChange(index: number): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiNativeSelectComponent<any>, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiNativeSelectComponent<any>, "select[tuiSelect]:not([labels]):not([multiple])", never, { "items": "items"; }, {}, never, never>;
}
