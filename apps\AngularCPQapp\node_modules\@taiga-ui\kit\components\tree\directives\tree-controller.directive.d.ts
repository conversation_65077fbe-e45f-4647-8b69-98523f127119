import { EventEmitter } from '@angular/core';
import { TuiTreeItemComponent } from '../components/tree-item/tree-item.component';
import { Tui<PERSON>reeAccessor, TuiTreeController } from '../misc/tree.interfaces';
import * as i0 from "@angular/core";
export declare class TuiTreeControllerDirective<T> implements TuiTreeController, TuiTreeAccessor<T> {
    fallback: boolean;
    map: Map<T, boolean>;
    readonly toggled: EventEmitter<T>;
    readonly items: Map<TuiTreeItemComponent, T>;
    register(item: TuiTreeItemComponent, value: T): void;
    unregister(item: TuiTreeItemComponent): void;
    isExpanded(item: TuiTreeItemComponent): boolean;
    toggle(item: TuiTreeItemComponent): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTreeControllerDirective<any>, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiTreeControllerDirective<any>, "[tuiTreeController][map]", ["tuiTreeController"], { "fallback": "tuiTreeController"; "map": "map"; }, { "toggled": "toggled"; }, never>;
}
