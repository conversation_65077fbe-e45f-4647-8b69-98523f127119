import { MaskitoOptions } from '@maskito/core';
import { AbstractTuiValueTransformer, TuiMapper } from '@taiga-ui/cdk';
import * as i0 from "@angular/core";
export declare class TuiUnmaskHandlerDirective extends AbstractTuiValueTransformer<string> {
    tuiUnmaskHandler: TuiMapper<string, string>;
    maskito: MaskitoOptions | null;
    fromControlValue(controlValue: unknown): string;
    toControlValue(value: string): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiUnmaskHandlerDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiUnmaskHandlerDirective, "[maskito][tuiUnmaskHandler]", never, { "tuiUnmaskHandler": "tuiUnmaskHandler"; "maskito": "maskito"; }, {}, never>;
}
