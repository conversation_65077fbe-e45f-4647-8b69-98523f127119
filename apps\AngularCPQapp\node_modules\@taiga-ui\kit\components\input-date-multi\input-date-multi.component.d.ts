import { ChangeDetectorRef, Type } from '@angular/core';
import { Ng<PERSON><PERSON><PERSON> } from '@angular/forms';
import { MaskitoOptions } from '@maskito/core';
import { AbstractTuiMultipleControl, AbstractTuiValueTransformer, TuiBooleanHandler, TuiDateMode, TuiDay, TuiFocusableElementAccessor, TuiMapper, TuiMonth, TuiTypedMapper } from '@taiga-ui/cdk';
import { TuiMarkerHandler, TuiSizeL, TuiSizeS, TuiTextfieldSizeDirective, TuiWithOptionalMinMax } from '@taiga-ui/core';
import { TuiStringifiableItem } from '@taiga-ui/kit/classes';
import { TuiInputDateOptions } from '@taiga-ui/kit/tokens';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiInputDateMultiComponent extends AbstractTuiMultipleControl<TuiDay> implements TuiWithOptionalMinMax<TuiDay>, TuiFocusableElementAccessor {
    readonly isMobile: boolean;
    private readonly mobileCalendar;
    readonly dateFormat: TuiDateMode;
    readonly dateSeparator: string;
    readonly dateTexts$: Observable<Record<TuiDateMode, string>>;
    readonly valueTransformer: AbstractTuiValueTransformer<readonly TuiDay[]> | null;
    private readonly options;
    private readonly textfieldSize;
    readonly doneWord$: Observable<string>;
    private readonly textfield?;
    private readonly inputTag?;
    private month;
    min: TuiDay | null;
    max: TuiDay | null;
    disabledItemHandler: TuiBooleanHandler<TuiDay>;
    markerHandler: TuiMarkerHandler;
    defaultActiveYearMonth: TuiMonth;
    expandable: boolean;
    inputHidden: boolean;
    search: string | null;
    placeholder: string;
    rows: number;
    maskitoOptions: MaskitoOptions;
    open: boolean;
    readonly filler$: Observable<string>;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, isMobile: boolean, mobileCalendar: Type<Record<string, any>> | null, dateFormat: TuiDateMode, dateSeparator: string, dateTexts$: Observable<Record<TuiDateMode, string>>, valueTransformer: AbstractTuiValueTransformer<readonly TuiDay[]> | null, options: TuiInputDateOptions, textfieldSize: TuiTextfieldSizeDirective, doneWord$: Observable<string>);
    tagValidator: TuiBooleanHandler<string>;
    onClick(): void;
    readonly disabledItemHandlerWrapper: TuiMapper<TuiBooleanHandler<string> | TuiBooleanHandler<TuiDay>, TuiBooleanHandler<TuiStringifiableItem<any> | string>>;
    readonly valueMapper: TuiTypedMapper<[
        readonly TuiDay[]
    ], ReadonlyArray<TuiStringifiableItem<TuiDay>>>;
    get size(): TuiSizeL | TuiSizeS;
    get nativeDropdownMode(): boolean;
    get computedMin(): TuiDay;
    get computedMax(): TuiDay;
    get nativeFocusableElement(): HTMLInputElement | null;
    get focused(): boolean;
    get computedMobile(): boolean;
    get calendarIcon(): TuiInputDateOptions['icon'];
    get computedActiveYearMonth(): TuiMonth;
    onIconClick(): void;
    onEnter(search: string): void;
    onValueChange(value: ReadonlyArray<TuiStringifiableItem<TuiDay>>): void;
    onDayClick(value: TuiDay): void;
    done(): void;
    onMonthChange(month: TuiMonth): void;
    onOpenChange(open: boolean): void;
    onFocused(focused: boolean): void;
    setDisabledState(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateMultiComponent, [{ optional: true; self: true; }, null, null, { optional: true; }, null, null, null, { optional: true; }, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputDateMultiComponent, "tui-input-date[multiple]", never, { "min": "min"; "max": "max"; "disabledItemHandler": "disabledItemHandler"; "markerHandler": "markerHandler"; "defaultActiveYearMonth": "defaultActiveYearMonth"; "expandable": "expandable"; "inputHidden": "inputHidden"; "search": "search"; "placeholder": "placeholder"; "rows": "rows"; "tagValidator": "tagValidator"; }, {}, never, ["*", "input"]>;
}
