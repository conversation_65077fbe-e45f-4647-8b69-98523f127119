import * as i0 from "@angular/core";
import * as i1 from "./radio-labeled.component";
import * as i2 from "@angular/common";
import * as i3 from "@angular/forms";
import * as i4 from "@taiga-ui/cdk";
import * as i5 from "@taiga-ui/kit/components/radio";
export declare class TuiRadioLabeledModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRadioLabeledModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiRadioLabeledModule, [typeof i1.TuiRadioLabeledComponent], [typeof i2.CommonModule, typeof i3.FormsModule, typeof i4.TuiFocusableModule, typeof i4.TuiFocusedModule, typeof i5.TuiRadioModule], [typeof i1.TuiRadioLabeledComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiRadioLabeledModule>;
}
