import { AbstractTuiNativeSelect } from '@taiga-ui/kit/abstract';
import { TuiItemsHandlers } from '@taiga-ui/kit/tokens';
import type { TuiSelectDirective } from '../select.directive';
import * as i0 from "@angular/core";
export declare class TuiNativeSelectGroupComponent<T> extends AbstractTuiNativeSelect<TuiSelectDirective, T> {
    items: readonly T[][] | null;
    labels: readonly string[];
    get stringify(): TuiItemsHandlers<T>['stringify'];
    selected(option: T): boolean;
    onValueChange(index: number): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiNativeSelectGroupComponent<any>, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiNativeSelectGroupComponent<any>, "select[tuiSelect][labels]:not([multiple])", never, { "items": "items"; "labels": "labels"; }, {}, never, never>;
}
