import { inject, InjectionToken } from '@angular/core';
import { WINDOW } from '@ng-web-apis/common';
import { tuiTypedFromEvent } from '@taiga-ui/cdk/observables';
import { map, shareReplay, startWith } from 'rxjs/operators';
export const TUI_WINDOW_SIZE = new InjectionToken('[TUI_WINDOW_SIZE]', {
    factory: () => {
        const w = inject(WINDOW);
        return tuiTypedFromEvent(w, 'resize').pipe(startWith(null), map(() => {
            var _a, _b;
            const width = Math.max(w.document.documentElement.clientWidth || 0, w.innerWidth || 0, ((_a = w.visualViewport) === null || _a === void 0 ? void 0 : _a.width) || 0);
            const height = Math.max(w.document.documentElement.clientHeight || 0, w.innerHeight || 0, ((_b = w.visualViewport) === null || _b === void 0 ? void 0 : _b.height) || 0);
            return {
                width,
                height,
                top: 0,
                left: 0,
                right: width,
                bottom: height,
            };
        }), shareReplay({ bufferSize: 1, refCount: true }));
    },
});
//# sourceMappingURL=data:application/json;base64,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