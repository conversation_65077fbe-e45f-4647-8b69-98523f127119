import * as i0 from "@angular/core";
import * as i1 from "./push.component";
import * as i2 from "./push.directive";
import * as i3 from "./push-alert.component";
import * as i4 from "./push-alert.directive";
import * as i5 from "@angular/common";
import * as i6 from "@tinkoff/ng-polymorpheus";
import * as i7 from "@taiga-ui/core";
export declare class TuiPushModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPushModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiPushModule, [typeof i1.TuiPushComponent, typeof i2.TuiPushDirective, typeof i3.TuiPushAlertComponent, typeof i4.TuiPushAlertDirective], [typeof i5.CommonModule, typeof i6.PolymorpheusModule, typeof i7.TuiButtonModule, typeof i7.TuiLinkModule, typeof i7.TuiSvgModule, typeof i7.TuiFormatDatePipeModule], [typeof i1.TuiPushComponent, typeof i2.TuiPushDirective, typeof i3.TuiPushAlertComponent, typeof i4.TuiPushAlertDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiPushModule>;
}
