import { ElementRef } from '@angular/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
/**
 * Native <input type='range' readonly> doesn't work.
 * This directive imitates this native behaviour.
 */
export declare class TuiSliderReadonlyDirective {
    readonly: boolean | string;
    constructor(el: ElementRef<HTMLInputElement>, doc: Document, destroy$: Observable<unknown>);
    preventEvent(event: Event): void;
    preventKeyboardInteraction(event: KeyboardEvent): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSliderReadonlyDirective, [null, null, { self: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiSliderReadonlyDirective, "input[tuiSlider][readonly]", never, { "readonly": "readonly"; }, {}, never>;
}
