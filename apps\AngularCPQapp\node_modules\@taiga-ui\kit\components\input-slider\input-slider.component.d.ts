import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TuiContextWithImplicit, TuiFocusableElementAccessor, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import { TuiDecimal, TuiTextfieldController, TuiWithOptionalMinMax } from '@taiga-ui/core';
import { TuiKeySteps } from '@taiga-ui/kit/types';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import * as i0 from "@angular/core";
export declare class TuiInputSliderComponent extends AbstractTuiControl<number> implements TuiWithOptionalMinMax<number>, TuiFocusableElementAccessor {
    readonly controller: TuiTextfieldController;
    private readonly inputNumberRef?;
    private readonly sliderRef?;
    min: number;
    max: number;
    quantum: number;
    steps: number;
    segments: number;
    keySteps: TuiKeySteps | null;
    valueContent: PolymorpheusContent<TuiContextWithImplicit<number>>;
    /** @deprecated use `tuiTextfieldPrefix` from {@link TuiTextfieldControllerModule} instead */
    textfieldPrefix: string;
    /** @deprecated use `tuiTextfieldPostfix` from {@link TuiTextfieldControllerModule} instead */
    textfieldPostfix: string;
    textfieldValue: number;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, controller: TuiTextfieldController);
    get prefix(): string;
    get postfix(): string;
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    get computedSteps(): number;
    get precision(): number;
    get decimal(): TuiDecimal;
    get showValueContent(): boolean;
    get step(): number;
    computeKeySteps(keySteps: TuiKeySteps | null, min: number, max: number): TuiKeySteps;
    focusTextInput(): void;
    safelyUpdateValue(value: number | null): void;
    onVerticalArrowKeyDown(coefficient: number): void;
    onSliderChange(newValue: number): void;
    onFocused(focused: boolean): void;
    writeValue(value: number | null): void;
    protected getFallbackValue(): number;
    private valueGuard;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputSliderComponent, [{ optional: true; self: true; }, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputSliderComponent, "tui-input-slider", never, { "min": "min"; "max": "max"; "quantum": "quantum"; "steps": "steps"; "segments": "segments"; "keySteps": "keySteps"; "valueContent": "valueContent"; "textfieldPrefix": "prefix"; "textfieldPostfix": "postfix"; }, {}, never, ["*"]>;
}
