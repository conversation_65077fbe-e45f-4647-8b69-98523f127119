import { DoCheck, TrackByFunction } from '@angular/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { TuiTreeChildrenDirective } from '../../directives/tree-children.directive';
import { TuiTreeContext } from '../../misc/tree.interfaces';
import { TuiTreeItemComponent } from '../tree-item/tree-item.component';
import * as i0 from "@angular/core";
export declare class TuiTreeComponent<T> implements DoCheck {
    readonly directive: TuiTreeChildrenDirective<T> | null;
    private readonly check$;
    value: T;
    readonly item?: TuiTreeItemComponent;
    readonly child?: TuiTreeComponent<T>;
    readonly children$: import("rxjs").Observable<readonly T[]>;
    constructor(directive: TuiTreeChildrenDirective<T> | null);
    trackBy: TrackByFunction<T>;
    content: PolymorpheusContent<TuiTreeContext<T>>;
    ngDoCheck(): void;
    checkChanges(): void;
    private get handler();
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTreeComponent<any>, [{ optional: true; }]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiTreeComponent<any>, "tui-tree[value]", never, { "value": "value"; "trackBy": "trackBy"; "content": "content"; }, {}, never, never>;
}
