import { ChangeDetectorRef, ElementRef, QueryList } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TuiFocusableElementAccessor, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import { TuiSizeS, TuiWithOptionalMinMax } from '@taiga-ui/core';
import { TuiKeySteps } from '@taiga-ui/kit/types';
import * as i0 from "@angular/core";
export declare class TuiRangeComponent extends AbstractTuiControl<[number, number]> implements TuiWithOptionalMinMax<number>, TuiFocusableElementAccessor {
    private readonly el;
    min: number;
    max: number;
    step: number;
    size: TuiSizeS;
    segments: number;
    keySteps: TuiKeySteps | null;
    slidersRefs: QueryList<ElementRef<HTMLInputElement>>;
    lastActiveThumb: 'left' | 'right';
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, el: ElementRef<HTMLElement>);
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    get fractionStep(): number;
    get computedKeySteps(): TuiKeySteps;
    get segmentWidthRatio(): number;
    get left(): number;
    get right(): number;
    onFocused(focused: boolean): void;
    changeByStep(coefficient: number, target: HTMLElement): void;
    processValue(value: number, right: boolean): void;
    getValueFromFraction(fraction: number): number;
    getPercentageFromValue(value: number): number;
    protected getFallbackValue(): [number, number];
    private computePureKeySteps;
    private updateStart;
    private updateEnd;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRangeComponent, [{ optional: true; self: true; }, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiRangeComponent, "tui-range", never, { "min": "min"; "max": "max"; "step": "step"; "size": "size"; "segments": "segments"; "keySteps": "keySteps"; }, {}, never, never>;
}
