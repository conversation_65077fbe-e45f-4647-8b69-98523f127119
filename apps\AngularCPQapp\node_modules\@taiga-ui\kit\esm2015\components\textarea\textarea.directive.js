import { Directive } from '@angular/core';
import { AbstractTuiTextfieldHost, tuiAsTextfieldHost } from '@taiga-ui/core';
import * as i0 from "@angular/core";
export class TuiTextareaDirective extends AbstractTuiTextfieldHost {
    onValueChange(value) {
        this.host.onValueChange(value);
    }
}
TuiTextareaDirective.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiTextareaDirective, deps: null, target: i0.ɵɵFactoryTarget.Directive });
TuiTextareaDirective.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: "12.0.0", version: "12.2.17", type: TuiTextareaDirective, selector: "tui-textarea", providers: [tuiAsTextfieldHost(TuiTextareaDirective)], usesInheritance: true, ngImport: i0 });
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiTextareaDirective, decorators: [{
            type: Directive,
            args: [{
                    selector: 'tui-textarea',
                    providers: [tuiAsTextfieldHost(TuiTextareaDirective)],
                }]
        }] });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGV4dGFyZWEuZGlyZWN0aXZlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vcHJvamVjdHMva2l0L2NvbXBvbmVudHMvdGV4dGFyZWEvdGV4dGFyZWEuZGlyZWN0aXZlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE9BQU8sRUFBQyxTQUFTLEVBQUMsTUFBTSxlQUFlLENBQUM7QUFDeEMsT0FBTyxFQUFDLHdCQUF3QixFQUFFLGtCQUFrQixFQUFDLE1BQU0sZ0JBQWdCLENBQUM7O0FBUTVFLE1BQU0sT0FBTyxvQkFBcUIsU0FBUSx3QkFBOEM7SUFDcEYsYUFBYSxDQUFDLEtBQWE7UUFDdkIsSUFBSSxDQUFDLElBQUksQ0FBQyxhQUFhLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDbkMsQ0FBQzs7a0hBSFEsb0JBQW9CO3NHQUFwQixvQkFBb0IsdUNBRmxCLENBQUMsa0JBQWtCLENBQUMsb0JBQW9CLENBQUMsQ0FBQzs0RkFFNUMsb0JBQW9CO2tCQUpoQyxTQUFTO21CQUFDO29CQUNQLFFBQVEsRUFBRSxjQUFjO29CQUN4QixTQUFTLEVBQUUsQ0FBQyxrQkFBa0Isc0JBQXNCLENBQUM7aUJBQ3hEIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtEaXJlY3RpdmV9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xuaW1wb3J0IHtBYnN0cmFjdFR1aVRleHRmaWVsZEhvc3QsIHR1aUFzVGV4dGZpZWxkSG9zdH0gZnJvbSAnQHRhaWdhLXVpL2NvcmUnO1xuXG5pbXBvcnQge1R1aVRleHRhcmVhQ29tcG9uZW50fSBmcm9tICcuL3RleHRhcmVhLmNvbXBvbmVudCc7XG5cbkBEaXJlY3RpdmUoe1xuICAgIHNlbGVjdG9yOiAndHVpLXRleHRhcmVhJyxcbiAgICBwcm92aWRlcnM6IFt0dWlBc1RleHRmaWVsZEhvc3QoVHVpVGV4dGFyZWFEaXJlY3RpdmUpXSxcbn0pXG5leHBvcnQgY2xhc3MgVHVpVGV4dGFyZWFEaXJlY3RpdmUgZXh0ZW5kcyBBYnN0cmFjdFR1aVRleHRmaWVsZEhvc3Q8VHVpVGV4dGFyZWFDb21wb25lbnQ+IHtcbiAgICBvblZhbHVlQ2hhbmdlKHZhbHVlOiBzdHJpbmcpOiB2b2lkIHtcbiAgICAgICAgdGhpcy5ob3N0Lm9uVmFsdWVDaGFuZ2UodmFsdWUpO1xuICAgIH1cbn1cbiJdfQ==