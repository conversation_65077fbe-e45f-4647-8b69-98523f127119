import * as i0 from "@angular/core";
import * as i1 from "./input-phone-international.component";
import * as i2 from "@angular/common";
import * as i3 from "@angular/forms";
import * as i4 from "@tinkoff/ng-polymorpheus";
import * as i5 from "@taiga-ui/core";
import * as i6 from "@taiga-ui/kit/components/input-phone";
import * as i7 from "@taiga-ui/kit/components/arrow";
import * as i8 from "@taiga-ui/cdk";
export declare class TuiInputPhoneInternationalModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputPhoneInternationalModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputPhoneInternationalModule, [typeof i1.TuiInputPhoneInternationalComponent], [typeof i2.CommonModule, typeof i3.FormsModule, typeof i4.PolymorpheusModule, typeof i5.TuiSvgModule, typeof i6.TuiInputPhoneModule, typeof i5.TuiGroupModule, typeof i5.TuiPrimitiveTextfieldModule, typeof i5.TuiTextfieldControllerModule, typeof i5.TuiHintModule, typeof i5.TuiHostedDropdownModule, typeof i5.TuiDataListModule, typeof i7.TuiArrowModule, typeof i5.TuiWrapperModule, typeof i8.TuiLetModule, typeof i8.TuiMapperPipeModule, typeof i8.TuiActiveZoneModule, typeof i5.TuiFlagPipeModule], [typeof i1.TuiInputPhoneInternationalComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputPhoneInternationalModule>;
}
