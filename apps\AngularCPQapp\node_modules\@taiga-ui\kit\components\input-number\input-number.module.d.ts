import * as i0 from "@angular/core";
import * as i1 from "./input-number.component";
import * as i2 from "./input-number.directive";
import * as i3 from "@angular/common";
import * as i4 from "@maskito/angular";
import * as i5 from "@taiga-ui/core";
import * as i6 from "@taiga-ui/kit/directives";
import * as i7 from "@tinkoff/ng-polymorpheus";
export declare class TuiInputNumberModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputNumberModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputNumberModule, [typeof i1.TuiInputNumberComponent, typeof i2.TuiInputNumberDirective], [typeof i3.CommonModule, typeof i4.MaskitoModule, typeof i5.TuiPrimitiveTextfieldModule, typeof i5.TuiTextfieldControllerModule, typeof i6.TuiValueAccessorModule, typeof i7.PolymorpheusModule, typeof i5.TuiButtonModule], [typeof i1.TuiInputNumberComponent, typeof i2.TuiInputNumberDirective, typeof i5.TuiTextfieldComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputNumberModule>;
}
