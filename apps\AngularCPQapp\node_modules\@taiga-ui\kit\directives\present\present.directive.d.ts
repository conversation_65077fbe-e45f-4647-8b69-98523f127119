import { ElementRef, OnD<PERSON>roy } from '@angular/core';
import * as i0 from "@angular/core";
export declare class TuiPresentDirective implements OnDestroy {
    private readonly visibility$;
    readonly tuiPresentChange: import("rxjs").Observable<boolean>;
    constructor({ nativeElement }: ElementRef<HTMLElement>, userAgent: string);
    /**
     * Someday animationcancel would work and mutation observer would not be needed:
     * https://drafts.csswg.org/css-animations/#eventdef-animationevent-animationcancel
     * It would also trigger on CSS like display: none on parent nodes which is awesome
     * but currently only works in Firefox
     * ___
     * TODO: remove MutationObserver when we bump versions of supported browsers:
     *** Safari 12+
     *** Chrome 83+
     * See: {@link https://caniuse.com/mdn-api_window_animationcancel_event}
     */
    onAnimation(visibility: boolean): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPresentDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiPresentDirective, "[tuiPresentChange]", never, {}, { "tuiPresentChange": "tuiPresentChange"; }, never>;
}
