import { Observable, pipe } from 'rxjs';
export function tuiZonefull(zone) {
    return source => new Observable(subscriber => source.subscribe({
        next: value => zone.run(() => subscriber.next(value)),
        error: (error) => zone.run(() => subscriber.error(error)),
        complete: () => zone.run(() => subscriber.complete()),
    }));
}
export function tuiZonefree(zone) {
    return source => new Observable(subscriber => zone.runOutsideAngular(() => source.subscribe(subscriber)));
}
export function tuiZoneOptimized(zone) {
    return pipe(tuiZonefree(zone), tuiZonefull(zone));
}
//# sourceMappingURL=data:application/json;base64,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