import { ElementRef, EventEmitter } from '@angular/core';
import { Observable } from 'rxjs';
import { TuiRangeComponent } from './range.component';
import * as i0 from "@angular/core";
export declare class TuiRangeChangeDirective {
    private readonly doc;
    private readonly el;
    private readonly range;
    /**
     * TODO replace with pointer events (when all supported browsers can handle them).
     * Don't forget to use setPointerCapture instead of listening all doc events
     */
    private readonly pointerDown$;
    private readonly pointerMove$;
    private readonly pointerUp$;
    readonly activeThumbChange: EventEmitter<"left" | "right">;
    constructor(doc: Document, el: ElementRef<HTMLElement>, range: TuiRangeComponent, destroy$: Observable<unknown>);
    private getFractionFromEvents;
    private detectActiveThumb;
    private findNearestActiveThumb;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRangeChangeDirective, [null, null, null, { self: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiRangeChangeDirective, "tui-range", never, {}, { "activeThumbChange": "activeThumbChange"; }, never>;
}
