import { ElementRef, InjectionToken, NgZone, Optional, Provider, Renderer2 } from '@angular/core';
import { TuiFocusableElementAccessor } from '@taiga-ui/cdk/interfaces';
import { TuiDestroyService } from '@taiga-ui/cdk/services';
import { Observable } from 'rxjs';
import { TuiDefaultAutofocusHandler } from './handlers/default.handler';
import { TuiIosAutofocusHandler } from './handlers/ios.handler';
export interface TuiAutofocusHandler {
    setFocus(): void;
}
export interface TuiAutofocusOptions {
    readonly delay: number;
}
export declare const TUI_AUTOFOCUS_DEFAULT_OPTIONS: TuiAutofocusOptions;
export declare const TUI_AUTOFOCUS_OPTIONS: InjectionToken<TuiAutofocusOptions>;
export declare function tuiAutoFocusOptionsProvider(options: Partial<TuiAutofocusOptions>): Provider;
export declare const TUI_AUTOFOCUS_HANDLER: InjectionToken<TuiAutofocusHandler>;
export declare const TUI_AUTOFOCUS_PROVIDERS: (typeof TuiDestroyService | {
    provide: InjectionToken<TuiAutofocusHandler>;
    useFactory: (focusable: TuiFocusableElementAccessor | null, el: ElementRef<HTMLElement>, animationFrame$: Observable<number>, renderer: Renderer2, zone: NgZone, win: Window, isIos: boolean) => TuiDefaultAutofocusHandler | TuiIosAutofocusHandler;
    deps: (typeof ElementRef | InjectionToken<Observable<number>> | typeof Renderer2 | typeof NgZone | Optional[])[];
})[];
