import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TuiFocusableElementAccessor, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import * as i0 from "@angular/core";
export declare class TuiInputInlineComponent extends AbstractTuiControl<number | string> implements TuiFocusableElementAccessor {
    private readonly native?;
    maxLength: number | null;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef);
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    onValueChange(value: string): void;
    onFocused(focused: boolean): void;
    protected getFallbackValue(): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputInlineComponent, [{ optional: true; self: true; }, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputInlineComponent, "tui-input-inline", never, { "maxLength": "maxLength"; }, {}, never, ["*"]>;
}
