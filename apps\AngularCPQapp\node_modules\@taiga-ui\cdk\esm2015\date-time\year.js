import { tuiAssert } from '@taiga-ui/cdk/classes';
import { tuiInRange, tuiNormalizeToIntNumber } from '@taiga-ui/cdk/utils/math';
import { MAX_YEAR, MIN_YEAR } from './date-time';
/**
 * Immutable year object
 * @nosideeffects
 */
export class TuiYear {
    constructor(year) {
        this.year = year;
        ngDevMode && tuiAssert.assert(TuiYear.isValidYear(year));
    }
    /**
     * Checks year for validity
     */
    static isValidYear(year) {
        return Number.isInteger(year) && tuiInRange(year, MIN_YEAR, MAX_YEAR + 1);
    }
    /**
     * Check if passed year is a leap year
     */
    static isLeapYear(year) {
        ngDevMode && tuiAssert.assert(TuiYear.isValidYear(year));
        return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);
    }
    /**
     * Returns amount of leap years from year 0 to the passed one
     */
    static getAbsoluteLeapYears(year) {
        ngDevMode && tuiAssert.assert(TuiYear.isValidYear(year));
        return Math.ceil(year / 400) + (Math.ceil(year / 4) - Math.ceil(year / 100));
    }
    static lengthBetween(from, to) {
        return to.year - from.year;
    }
    /**
     * Normalizes year by clamping it between min and max years
     */
    static normalizeYearPart(year) {
        return tuiNormalizeToIntNumber(year, MIN_YEAR, MAX_YEAR);
    }
    get formattedYear() {
        return String(this.year).padStart(4, '0');
    }
    get isLeapYear() {
        return TuiYear.isLeapYear(this.year);
    }
    /**
     * Returns amount of leap years from year 0 to current
     */
    get absoluteLeapYears() {
        return TuiYear.getAbsoluteLeapYears(this.year);
    }
    /**
     * Passed year is after current
     */
    yearBefore({ year }) {
        return this.year < year;
    }
    /**
     * Passed year is the same or after current
     */
    yearSameOrBefore({ year }) {
        return this.year <= year;
    }
    /**
     * Passed year is the same as current
     */
    yearSame({ year }) {
        return this.year === year;
    }
    /**
     * Passed year is either the same of before the current
     */
    yearSameOrAfter({ year }) {
        return this.year >= year;
    }
    /**
     * Passed year is before current
     */
    yearAfter({ year }) {
        return this.year > year;
    }
    /**
     * Immutably offsets year
     */
    append({ year = 0 }) {
        ngDevMode && tuiAssert.assert(Number.isInteger(year));
        const resultYear = this.year + year;
        ngDevMode && tuiAssert.assert(TuiYear.isValidYear(resultYear));
        return new TuiYear(resultYear);
    }
    toString() {
        return this.formattedYear;
    }
    valueOf() {
        return this.year;
    }
    /**
     * Returns the primitive value of the given Date object.
     * Depending on the argument, the method can return either a string or a number.
     * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/@@toPrimitive
     */
    [Symbol.toPrimitive](hint) {
        return Date.prototype[Symbol.toPrimitive].call(this, hint);
    }
    toJSON() {
        return this.formattedYear;
    }
}
//# sourceMappingURL=data:application/json;base64,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