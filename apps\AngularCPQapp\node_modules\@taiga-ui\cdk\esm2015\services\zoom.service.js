import { ElementRef, Inject, Injectable } from '@angular/core';
import { tuiPreventDefault, tuiTypedFromEvent } from '@taiga-ui/cdk/observables';
import { TUI_ZOOM_OPTIONS } from '@taiga-ui/cdk/tokens';
import { tuiDistanceBetweenTouches } from '@taiga-ui/cdk/utils';
import { merge, Observable } from 'rxjs';
import { filter, map, scan, switchMap, takeUntil } from 'rxjs/operators';
import * as i0 from "@angular/core";
const TOUCH_SENSITIVITY = 0.01;
export class TuiZoomService extends Observable {
    constructor({ nativeElement }, { wheelSensitivity }) {
        super(subscriber => {
            merge(tuiTypedFromEvent(nativeElement, 'touchstart', { passive: true }).pipe(filter(({ touches }) => touches.length > 1), switchMap(startEvent => tuiTypedFromEvent(nativeElement, 'touchmove', {
                passive: true,
            }).pipe(tuiPreventDefault(), scan((prev, event) => {
                const distance = tuiDistanceBetweenTouches(event);
                return {
                    event,
                    distance,
                    delta: (distance - prev.distance) *
                        TOUCH_SENSITIVITY,
                };
            }, {
                event: startEvent,
                distance: tuiDistanceBetweenTouches(startEvent),
                delta: 0,
            }), map(({ event, delta }) => {
                const clientX = (event.touches[0].clientX +
                    event.touches[1].clientX) /
                    2;
                const clientY = (event.touches[0].clientY +
                    event.touches[1].clientY) /
                    2;
                return { clientX, clientY, delta, event };
            }), takeUntil(tuiTypedFromEvent(nativeElement, 'touchend'))))), tuiTypedFromEvent(nativeElement, 'wheel', { passive: false }).pipe(tuiPreventDefault(), map(wheel => ({
                clientX: wheel.clientX,
                clientY: wheel.clientY,
                delta: -wheel.deltaY * wheelSensitivity,
                event: wheel,
            })))).subscribe(subscriber);
        });
    }
}
TuiZoomService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiZoomService, deps: [{ token: ElementRef }, { token: TUI_ZOOM_OPTIONS }], target: i0.ɵɵFactoryTarget.Injectable });
TuiZoomService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiZoomService });
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiZoomService, decorators: [{
            type: Injectable
        }], ctorParameters: function () { return [{ type: i0.ElementRef, decorators: [{
                    type: Inject,
                    args: [ElementRef]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TUI_ZOOM_OPTIONS]
                }] }]; } });
//# sourceMappingURL=data:application/json;base64,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