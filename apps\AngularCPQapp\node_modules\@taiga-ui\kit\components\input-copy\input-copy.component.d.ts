import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TuiFocusableElementAccessor, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import { TuiSizeL, TuiSizeS, TuiTextfieldSizeDirective } from '@taiga-ui/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { Observable } from 'rxjs';
import { TuiInputCopyOptions } from './input-copy.options';
import * as i0 from "@angular/core";
export declare class TuiInputCopyComponent extends AbstractTuiControl<string> implements TuiFocusableElementAccessor {
    private readonly doc;
    private readonly copyTexts$;
    private readonly options;
    private readonly textfieldSize;
    private readonly textfield?;
    private readonly copied$;
    successMessage: PolymorpheusContent<any>;
    messageDirection: import("@taiga-ui/core").TuiHintDirection;
    messageAppearance: string;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, doc: Document, copyTexts$: Observable<[string, string]>, options: TuiInputCopyOptions, textfieldSize: TuiTextfieldSizeDirective);
    get size(): TuiSizeL | TuiSizeS;
    get hintText$(): Observable<PolymorpheusContent>;
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    get icon(): TuiInputCopyOptions['icon'];
    onValueChange(value: string): void;
    onFocused(focused: boolean): void;
    copy(): void;
    protected getFallbackValue(): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputCopyComponent, [{ optional: true; self: true; }, null, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputCopyComponent, "tui-input-copy", never, { "successMessage": "successMessage"; "messageDirection": "messageDirection"; "messageAppearance": "messageAppearance"; }, {}, never, ["*", "input"]>;
}
