import * as i0 from "@angular/core";
import * as i1 from "./project-class.directive";
export declare class TuiProjectClassModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiProjectClassModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiProjectClassModule, [typeof i1.TuiProjectClassDirective], never, [typeof i1.TuiProjectClassDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiProjectClassModule>;
}
