import { ElementRef, EventEmitter } from '@angular/core';
import { TuiBrightness, TuiCommonIcons, TuiSizeL, TuiSizeS, TuiSizeXS } from '@taiga-ui/core';
import { TuiStatus } from '@taiga-ui/kit/types';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { Observable } from 'rxjs';
import { TuiTagOptions } from './tag.options';
import * as i0 from "@angular/core";
export declare class TuiTagComponent {
    private readonly el;
    readonly mode$: Observable<TuiBrightness | null>;
    private readonly options;
    readonly icons: TuiCommonIcons;
    value: string;
    editable: boolean;
    separator: RegExp | string;
    maxLength: number | null;
    size: TuiSizeL | TuiSizeS;
    showLoader: boolean;
    status: TuiStatus;
    hoverable: boolean;
    removable: boolean;
    disabled: boolean;
    autoColor: boolean;
    leftContent: PolymorpheusContent;
    readonly edited: EventEmitter<string>;
    editing: boolean;
    editedText: string | null;
    set input(input: ElementRef<HTMLInputElement>);
    constructor(el: ElementRef<HTMLElement>, mode$: Observable<TuiBrightness | null>, options: TuiTagOptions, icons: TuiCommonIcons);
    get backgroundColor(): string | null;
    get canRemove(): boolean;
    get displayText(): string;
    get loaderSize(): TuiSizeXS;
    edit(event: Event): void;
    remove(event: Event): void;
    onInput(value: string): void;
    onKeyDown(event: KeyboardEvent): void;
    onBlur(): void;
    private get canEdit();
    private stopEditing;
    private save;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTagComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiTagComponent, "tui-tag, a[tuiTag], button[tuiTag]", never, { "value": "value"; "editable": "editable"; "separator": "separator"; "maxLength": "maxLength"; "size": "size"; "showLoader": "showLoader"; "status": "status"; "hoverable": "hoverable"; "removable": "removable"; "disabled": "disabled"; "autoColor": "autoColor"; "leftContent": "leftContent"; }, { "edited": "edited"; }, never, never>;
}
