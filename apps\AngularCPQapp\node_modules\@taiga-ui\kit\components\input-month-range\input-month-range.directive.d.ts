import { Do<PERSON><PERSON><PERSON> } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@taiga-ui/cdk';
import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { Observable } from 'rxjs';
import { TuiInputMonthRangeComponent } from './input-month-range.component';
import * as i0 from "@angular/core";
export declare class TuiInputMonthRangeDirective extends AbstractTuiTextfieldHost<TuiInputMonthRangeComponent> implements DoCheck {
    private readonly value$;
    private localizedValue;
    constructor(host: TuiInputMonthRangeComponent, formatter: TuiHandler<TuiMonth | null, Observable<string>>, destroy$: Observable<unknown>);
    get readOnly(): boolean;
    get value(): string;
    ngDoCheck(): void;
    onValueChange(value: string): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputMonthRangeDirective, [null, null, { self: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputMonthRangeDirective, "tui-input-month-range", never, {}, {}, never>;
}
