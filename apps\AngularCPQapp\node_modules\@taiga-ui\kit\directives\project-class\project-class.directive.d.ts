import { AfterViewChecked, ElementRef } from '@angular/core';
import * as i0 from "@angular/core";
/**
 * A directive for projecting classes from nested children to host
 */
export declare class TuiProjectClassDirective implements AfterViewChecked {
    private readonly el;
    classNames: readonly string[];
    constructor(el: ElementRef<HTMLElement>);
    ngAfterViewChecked(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiProjectClassDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiProjectClassDirective, "[tuiProjectClass]", never, { "classNames": "tuiProjectClass"; }, {}, never>;
}
