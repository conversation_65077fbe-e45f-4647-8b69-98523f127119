"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.wrapWithTuiRootComponent = void 0;
const tslib_1 = require("tslib");
const workspace_1 = require("@schematics/angular/utility/workspace");
const ng_morph_1 = require("ng-morph");
const constants_1 = require("../../constants");
const get_component_from_identifier_1 = require("../../utils/get-component-from-identifier");
const get_project_target_options_1 = require("../../utils/get-project-target-options");
const get_projects_1 = require("../../utils/get-projects");
const get_standalone_bootstrap_function_1 = require("../../utils/get-standalone-bootstrap-function");
function addTuiRootComponent(filePath, context, tree) {
    const buffer = tree.read(filePath);
    if (!buffer) {
        context.logger.error(`Could not read the default template file within the project ${filePath}`);
        context.logger.info('Consider manually wrapping content of your app with tui-root');
        return;
    }
    const htmlContent = buffer.toString();
    const openTag = '<tui-root>\n';
    const closeTag = '\n</tui-root>';
    if (htmlContent.includes(openTag)) {
        return;
    }
    const recorder = tree.beginUpdate(filePath);
    recorder.insertLeft(0, openTag);
    recorder.insertLeft(htmlContent.length, closeTag);
    tree.commitUpdate(recorder);
    context.logger.info(`Content of the app was wrapped with tui-root component in ${filePath}`);
}
function getAppTemplatePath(mainPath) {
    const standaloneBootstrapFunction = get_standalone_bootstrap_function_1.getStandaloneBootstrapFunction(mainPath);
    if (standaloneBootstrapFunction) {
        const [componentIdentifier] = standaloneBootstrapFunction.getArguments();
        const component = get_component_from_identifier_1.getComponentFromIdentifier(componentIdentifier);
        return component && getTemplatePathFromComponent(component);
    }
    const mainModule = ng_morph_1.getMainModule(mainPath);
    if (!mainModule) {
        return;
    }
    const mainInitializer = getTemplateInitializer(mainModule, 'NgModule', 'declarations');
    if (!ng_morph_1.Node.isArrayLiteralExpression(mainInitializer)) {
        return;
    }
    const appIdentifier = mainInitializer.getElements()[0];
    const appComponent = appIdentifier.getDefinitionNodes()[0];
    return getTemplatePathFromComponent(appComponent);
}
function getTemplatePathFromComponent(component) {
    const templateInitializer = getTemplateInitializer(component, 'Component', 'templateUrl');
    const appComponentPath = component.getSourceFile().getFilePath().split('/');
    return `${appComponentPath
        .splice(0, appComponentPath.length - 1)
        .join('/')}/${templateInitializer === null || templateInitializer === void 0 ? void 0 : templateInitializer.getText().replace(/['"]/g, '')}`;
}
function getTemplateInitializer(classDeclaration, decoratorName, propertyName) {
    const decorator = classDeclaration.getDecorator(decoratorName);
    if (!decorator) {
        return;
    }
    const [metadata] = decorator.getArguments();
    if (!ng_morph_1.Node.isObjectLiteralExpression(metadata)) {
        return;
    }
    const property = metadata.getProperty(propertyName);
    if (!ng_morph_1.Node.isPropertyAssignment(property)) {
        return;
    }
    return property.getInitializer();
}
function wrapWithTuiRootComponent(options) {
    return (tree, context) => tslib_1.__awaiter(this, void 0, void 0, function* () {
        const workspace = yield workspace_1.getWorkspace(tree);
        const project = get_projects_1.getProjects(options, workspace)[0];
        if (!project) {
            return;
        }
        const buildOptions = get_project_target_options_1.getProjectTargetOptions(project, 'build');
        ng_morph_1.setActiveProject(ng_morph_1.createProject(tree, '/', constants_1.ALL_FILES));
        const appTemplatePath = getAppTemplatePath(buildOptions.main);
        ng_morph_1.saveActiveProject();
        if (!appTemplatePath) {
            context.logger.error('Could not find the default main template file for this project.');
            context.logger.info('Consider manually wrapping content of your app with tui-root');
            context.logger.info('More information at https://taiga-ui.dev/getting-started');
            return;
        }
        addTuiRootComponent(appTemplatePath, context, tree);
    });
}
exports.wrapWithTuiRootComponent = wrapWithTuiRootComponent;
