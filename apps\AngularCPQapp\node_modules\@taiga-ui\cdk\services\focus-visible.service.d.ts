import { ChangeDetectorRef, ElementRef } from '@angular/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
/**
 * Service to imitate :focus-visible
 * (https://developer.mozilla.org/en-US/docs/Web/CSS/:focus-visible)
 * in browsers that do not support it
 */
export declare class TuiFocusVisibleService extends Observable<boolean> {
    private readonly focusVisible$;
    constructor({ nativeElement }: ElementRef<Element>, cdr: ChangeDetectorRef, destroy$: Observable<void>);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiFocusVisibleService, [null, null, { self: true; }]>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TuiFocusVisibleService>;
}
