import { Injector } from '@angular/core';
import { Validator } from '@angular/forms';
import * as i0 from "@angular/core";
export declare class TuiUnfinishedValidatorDirective implements Validator {
    private readonly injector;
    private readonly message;
    readonly validate: import("@angular/forms").ValidatorFn;
    constructor(injector: Injector, message: string | null);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiUnfinishedValidatorDirective, [null, { attribute: "tuiUnfinishedValidator"; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiUnfinishedValidatorDirective, "[tuiUnfinishedValidator]", never, {}, {}, never>;
}
