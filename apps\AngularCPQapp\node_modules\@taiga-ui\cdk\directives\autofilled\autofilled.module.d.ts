import * as i0 from "@angular/core";
import * as i1 from "./autofilled.directive";
import * as i2 from "./autofilled-style.component";
export declare class TuiAutofilledModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiAutofilledModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiAutofilledModule, [typeof i1.TuiAutofilledDirective, typeof i2.TuiAutofilledStyleComponent], never, [typeof i1.TuiAutofilledDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiAutofilledModule>;
}
