import * as i0 from "@angular/core";
import * as i1 from "./focus-visible.directive";
export declare class TuiFocusVisibleModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiFocusVisibleModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiFocusVisibleModule, [typeof i1.TuiFocusVisibleDirective], never, [typeof i1.TuiFocusVisibleDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiFocusVisibleModule>;
}
