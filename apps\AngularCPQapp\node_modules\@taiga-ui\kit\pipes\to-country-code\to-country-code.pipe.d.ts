import { PipeTransform } from '@angular/core';
import { TuiCountryIsoCode } from '@taiga-ui/i18n';
import * as i0 from "@angular/core";
export declare class TuiToCountryCodePipe implements PipeTransform {
    private readonly countriesMasks;
    constructor(countriesMasks: Record<TuiCountryIsoCode, string>);
    transform(value: string, countries: readonly TuiCountryIsoCode[]): TuiCountryIsoCode | undefined;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiToCountryCodePipe, never>;
    static ɵpipe: i0.ɵɵPipeDeclaration<TuiToCountryCodePipe, "tuiToCountryCode">;
}
