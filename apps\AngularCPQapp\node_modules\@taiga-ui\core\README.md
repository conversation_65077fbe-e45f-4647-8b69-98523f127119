# Taiga UI — Core

[![npm version](https://img.shields.io/npm/v/@taiga-ui/core.svg)](https://npmjs.com/package/@taiga-ui/core)
[![npm bundle size](https://img.shields.io/bundlephobia/minzip/@taiga-ui/core)](https://bundlephobia.com/result?p=@taiga-ui/core)
[![Discord](https://img.shields.io/discord/748677963142135818?color=7289DA&label=%23taiga-ui&logo=discord&logoColor=white)](https://discord.gg/Us8d8JVaTg)

[Website](https://taiga-ui.dev) • [Documentation](https://taiga-ui.dev/getting-started) •
[Core team](https://github.com/taiga-family/taiga-ui/#core-team)

> Basic elements needed to develop components, directives and more using Taiga UI design system

It's a part of [**Taiga UI**](https://github.com/taiga-family/taiga-ui) that is fully-treeshakable Angular UI Kit
consisting of multiple base libraries and several add-ons

## How to install

```
npm i @taiga-ui/{cdk,core}
```

Don't forget that Taiga UI is fully-treeshakable. **You can import even just one entity from our library** and be sure
that there is no redundant code in your bundle. Bundlphobia badge shows size of the whole library.

## Docs

See our [Documentation](https://taiga-ui.dev/getting-started)
