import { ChangeDetectorRef, Injector, OnInit, TrackByFunction } from '@angular/core';
import { TuiDialog, TuiTypedMapper } from '@taiga-ui/cdk/types';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiAlertHostComponent<T extends TuiDialog<unknown, unknown>> implements OnInit {
    private readonly allAlerts;
    private readonly injector;
    private readonly destroy$;
    private readonly cdr;
    alerts: ReadonlyArray<readonly T[]>;
    constructor(allAlerts: Array<Observable<readonly T[]>>, injector: Injector, destroy$: Observable<void>, cdr: ChangeDetectorRef);
    readonly trackBy: TrackByFunction<readonly T[]>;
    ngOnInit(): void;
    readonly mapper: TuiTypedMapper<[unknown], Injector>;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiAlertHostComponent<any>, [null, null, { self: true; }, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiAlertHostComponent<any>, "tui-alert-host", never, {}, {}, never, never>;
}
