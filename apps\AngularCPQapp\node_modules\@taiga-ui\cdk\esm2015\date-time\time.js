import { tuiAsser<PERSON> } from '@taiga-ui/cdk/classes';
import { tuiInRange } from '@taiga-ui/cdk/utils/math';
import { HOURS_IN_DAY, MILLISECONDS_IN_DAY, MILLISECONDS_IN_HOUR, MILLISECONDS_IN_MINUTE, MILLISECONDS_IN_SECOND, MINUTES_IN_HOUR, SECONDS_IN_MINUTE, } from './date-time';
/**
 * Immutable time object with hours, minutes, seconds and ms
 */
export class TuiTime {
    constructor(hours, minutes, seconds = 0, ms = 0) {
        this.hours = hours;
        this.minutes = minutes;
        this.seconds = seconds;
        this.ms = ms;
        ngDevMode &&
            tuiAssert.assert(
            // Currently `TuiTime` could have hours more than 23
            // in order to not break current behaviour of `isValidTime` the logic is duplicated
            Number.isInteger(hours) &&
                tuiInRange(hours, 0, Infinity) &&
                Number.isInteger(minutes) &&
                tuiInRange(minutes, 0, MINUTES_IN_HOUR) &&
                Number.isInteger(seconds) &&
                tuiInRange(seconds, 0, SECONDS_IN_MINUTE) &&
                Number.isInteger(ms) &&
                tuiInRange(ms, 0, 1000), 'Time must be real, but got:', hours, minutes, seconds, ms);
    }
    /**
     * Checks if time is valid
     */
    static isValidTime(hours, minutes, seconds = 0, ms = 0) {
        return (Number.isInteger(hours) &&
            tuiInRange(hours, 0, HOURS_IN_DAY) &&
            Number.isInteger(minutes) &&
            tuiInRange(minutes, 0, MINUTES_IN_HOUR) &&
            Number.isInteger(seconds) &&
            tuiInRange(seconds, 0, SECONDS_IN_MINUTE) &&
            Number.isInteger(ms) &&
            tuiInRange(ms, 0, 1000));
    }
    /**
     * Current UTC time.
     */
    static current() {
        return TuiTime.fromAbsoluteMilliseconds(Date.now() % MILLISECONDS_IN_DAY);
    }
    /**
     * Current time in local timezone
     */
    static currentLocal() {
        const date = new Date();
        return TuiTime.fromAbsoluteMilliseconds((Date.now() - date.getTimezoneOffset() * MILLISECONDS_IN_MINUTE) %
            MILLISECONDS_IN_DAY);
    }
    /**
     * Calculates TuiTime from milliseconds
     */
    static fromAbsoluteMilliseconds(milliseconds) {
        ngDevMode && tuiAssert.assert(Number.isInteger(milliseconds));
        ngDevMode &&
            tuiAssert.assert(tuiInRange(milliseconds, 0, MILLISECONDS_IN_DAY), `Milliseconds must be below ${MILLISECONDS_IN_DAY} (milliseconds in a day).`);
        const hours = Math.floor(milliseconds / MILLISECONDS_IN_HOUR);
        const minutes = Math.floor((milliseconds % MILLISECONDS_IN_HOUR) / MILLISECONDS_IN_MINUTE);
        const seconds = Math.floor(((milliseconds % MILLISECONDS_IN_HOUR) % MILLISECONDS_IN_MINUTE) / 1000) || 0;
        const ms = Math.floor(((milliseconds % MILLISECONDS_IN_HOUR) % MILLISECONDS_IN_MINUTE) % 1000) || 0;
        return new TuiTime(hours, minutes, seconds, ms);
    }
    /**
     * Parses string into TuiTime object
     */
    static fromString(time) {
        const hours = Number(time.slice(0, 2));
        const minutes = Number(time.slice(3, 5));
        const seconds = Number(time.slice(6, 8)) || 0;
        const ms = Number(time.slice(9, 12)) || 0;
        return new TuiTime(hours, minutes, seconds, ms);
    }
    /**
     * Converts Date object into TuiTime
     * @param date
     */
    static fromLocalNativeDate(date) {
        return new TuiTime(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());
    }
    /**
     * Shifts time by hours and minutes
     */
    shift({ hours = 0, minutes = 0, seconds = 0, ms = 0 }) {
        const totalMs = this.toAbsoluteMilliseconds() +
            hours * MILLISECONDS_IN_HOUR +
            minutes * MILLISECONDS_IN_MINUTE +
            seconds * MILLISECONDS_IN_SECOND +
            ms;
        const totalSeconds = Math.floor(totalMs / MILLISECONDS_IN_SECOND);
        const totalMinutes = Math.floor(totalSeconds / SECONDS_IN_MINUTE);
        const totalHours = Math.floor(totalMinutes / MINUTES_IN_HOUR);
        return new TuiTime(this.normalizeToRange(totalHours, HOURS_IN_DAY), this.normalizeToRange(totalMinutes, MINUTES_IN_HOUR), this.normalizeToRange(totalSeconds, SECONDS_IN_MINUTE), this.normalizeToRange(totalMs, MILLISECONDS_IN_SECOND));
    }
    /**
     * Converts TuiTime to string
     */
    toString(mode) {
        const needAddMs = mode === 'HH:MM:SS.MSS' || (!mode && this.ms > 0);
        const needAddSeconds = needAddMs || mode === 'HH:MM:SS' || (!mode && this.seconds > 0);
        return (`${this.formatTime(this.hours)}:${this.formatTime(this.minutes)}` +
            `${needAddSeconds ? `:${this.formatTime(this.seconds)}` : ''}` +
            `${needAddMs ? `.${this.formatTime(this.ms, 3)}` : ''}`);
    }
    valueOf() {
        return this.toAbsoluteMilliseconds();
    }
    /**
     * Returns the primitive value of the given Date object.
     * Depending on the argument, the method can return either a string or a number.
     * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/@@toPrimitive
     */
    [Symbol.toPrimitive](hint) {
        return Date.prototype[Symbol.toPrimitive].call(this, hint);
    }
    /**
     * Converts TuiTime to milliseconds
     */
    toAbsoluteMilliseconds() {
        return (this.hours * MILLISECONDS_IN_HOUR +
            this.minutes * MILLISECONDS_IN_MINUTE +
            this.seconds * 1000 +
            this.ms);
    }
    formatTime(time, digits = 2) {
        return String(time).padStart(digits, '0');
    }
    normalizeToRange(value, modulus) {
        return ((value % modulus) + modulus) % modulus;
    }
}
//# sourceMappingURL=data:application/json;base64,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