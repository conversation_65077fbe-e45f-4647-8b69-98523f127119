import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { MaskitoOptions } from '@maskito/core';
import { AbstractTuiControl, AbstractTuiValueTransformer, TuiActiveZoneDirective, TuiBooleanHandler, TuiContextWithImplicit, TuiDateMode, TuiDay, TuiFocusableElementAccessor, TuiMonth, TuiTime, TuiTimeMode } from '@taiga-ui/cdk';
import { TuiSizeL, TuiSizeS, TuiTextfieldSizeDirective, TuiWithOptionalMinMax } from '@taiga-ui/core';
import { TuiInputDateOptions } from '@taiga-ui/kit/tokens';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiInputDateTimeComponent extends AbstractTuiControl<[TuiDay | null, TuiTime | null]> implements TuiWithOptionalMinMax<TuiDay | [TuiDay | null, TuiTime | null] | null>, TuiFocusableElementAccessor {
    readonly dateFormat: TuiDateMode;
    readonly dateSeparator: string;
    readonly timeTexts$: Observable<Record<TuiTimeMode, string>>;
    readonly dateTexts$: Observable<Record<TuiDateMode, string>>;
    readonly valueTransformer: AbstractTuiValueTransformer<[
        TuiDay | null,
        TuiTime | null
    ]> | null;
    private readonly options;
    readonly isMobile: boolean;
    readonly isIos: boolean;
    private readonly textfieldSize;
    private readonly textfield?;
    private month;
    private readonly timeMode$;
    min: TuiDay | [TuiDay | null, TuiTime | null] | null;
    max: TuiDay | [TuiDay | null, TuiTime | null] | null;
    disabledItemHandler: TuiBooleanHandler<TuiDay>;
    defaultActiveYearMonth: TuiMonth;
    set timeMode(value: TuiTimeMode);
    get timeMode(): TuiTimeMode;
    open: boolean;
    readonly type: TuiContextWithImplicit<TuiActiveZoneDirective>;
    readonly filler$: Observable<string>;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, dateFormat: TuiDateMode, dateSeparator: string, timeTexts$: Observable<Record<TuiTimeMode, string>>, dateTexts$: Observable<Record<TuiDateMode, string>>, valueTransformer: AbstractTuiValueTransformer<[
        TuiDay | null,
        TuiTime | null
    ]> | null, options: TuiInputDateOptions, isMobile: boolean, isIos: boolean, textfieldSize: TuiTextfieldSizeDirective);
    get size(): TuiSizeL | TuiSizeS;
    get computedMin(): TuiDay | [TuiDay, TuiTime];
    get computedMax(): TuiDay | [TuiDay, TuiTime];
    get fillerLength(): number;
    get maskOptions(): MaskitoOptions;
    get nativeFocusableElement(): HTMLInputElement | null;
    get focused(): boolean;
    get calendarIcon(): TuiInputDateOptions['icon'];
    private get nativePicker();
    get showNativePicker(): boolean;
    get computedValue(): string;
    get calendarValue(): TuiDay | null;
    get calendarMinDay(): TuiDay;
    get calendarMaxDay(): TuiDay;
    get computedActiveYearMonth(): TuiMonth;
    get nativeValue(): string;
    set nativeValue(value: string);
    onClick(): void;
    onValueChange(value: string): void;
    onDayClick(day: TuiDay): void;
    onMonthChange(month: TuiMonth): void;
    onOpenChange(open: boolean): void;
    onFocused(focused: boolean): void;
    setDisabledState(): void;
    writeValue(value: [TuiDay | null, TuiTime | null] | null): void;
    protected getFallbackValue(): [TuiDay | null, TuiTime | null];
    protected valueIdenticalComparator(oldValue: [TuiDay | null, TuiTime | null], newValue: [TuiDay | null, TuiTime | null]): boolean;
    private calculateMask;
    private getDateTimeString;
    private updateNativeValue;
    private clampTime;
    private trimTrailingSeparator;
    private toNativeDate;
    private toTuiDay;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateTimeComponent, [{ optional: true; self: true; }, null, null, null, null, null, { optional: true; }, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputDateTimeComponent, "tui-input-date-time", never, { "min": "min"; "max": "max"; "disabledItemHandler": "disabledItemHandler"; "defaultActiveYearMonth": "defaultActiveYearMonth"; "timeMode": "timeMode"; }, {}, never, ["*", "input"]>;
}
