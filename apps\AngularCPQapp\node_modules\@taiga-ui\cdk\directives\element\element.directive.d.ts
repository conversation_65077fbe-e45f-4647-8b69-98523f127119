import { ElementRef } from '@angular/core';
import * as i0 from "@angular/core";
export declare class TuiElementDirective<T extends Element = HTMLElement> implements ElementRef<T> {
    nativeElement: T;
    constructor({ nativeElement }: ElementRef<T>);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiElementDirective<any>, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiElementDirective<any>, "[tuiElement]", ["elementRef"], {}, {}, never>;
}
