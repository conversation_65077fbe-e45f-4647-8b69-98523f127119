{"name": "@taiga-ui/cdk", "version": "3.117.0", "description": "Base library for creating Angular components and applications using Taiga UI principles regarding of actual visual appearance", "keywords": ["angular", "cdk", "component", "development", "kit", "service", "directive"], "homepage": "https://github.com/taiga-family/taiga-ui", "repository": "https://github.com/taiga-family/taiga-ui", "license": "Apache-2.0", "dependencies": {"@ng-web-apis/common": "^3.2.3", "@ng-web-apis/mutation-observer": "^3.2.3", "@ng-web-apis/resize-observer": "^3.2.3", "@tinkoff/ng-event-plugins": "^3.2.0", "@tinkoff/ng-polymorpheus": "^4.3.0", "tslib": "^2.7.0"}, "peerDependencies": {"@angular/animations": ">=12.0.0", "@angular/common": ">=12.0.0", "@angular/core": ">=12.0.0", "@angular/forms": ">=12.0.0", "rxjs": ">=6.0.0"}, "optionalDependencies": {"ng-morph": "^4.8.2", "parse5": "^6.0.1"}, "ng-update": {"migrations": "./schematics/migration.json", "packageGroup": ["@taiga-ui/i18n", "@taiga-ui/cdk", "@taiga-ui/core", "@taiga-ui/kit", "@taiga-ui/styles", "@taiga-ui/testing", "@taiga-ui/addon-doc", "@taiga-ui/addon-charts", "@taiga-ui/addon-commerce", "@taiga-ui/icons", "@taiga-ui/addon-preview", "@taiga-ui/addon-tablebars", "@taiga-ui/addon-table", "@taiga-ui/addon-mobile", "@taiga-ui/layout", "@taiga-ui/experimental", "@taiga-ui/proprietary-banking", "@taiga-ui/proprietary-core", "@taiga-ui/proprietary-icons", "@taiga-ui/proprietary-navigation", "@taiga-ui/proprietary-tds-icons", "@taiga-ui/proprietary-tds-palette"]}, "schematics": "./schematics/collection.json", "main": "bundles/taiga-ui-cdk.umd.js", "module": "fesm2015/taiga-ui-cdk.js", "es2015": "fesm2015/taiga-ui-cdk.js", "esm2015": "esm2015/taiga-ui-cdk.js", "fesm2015": "fesm2015/taiga-ui-cdk.js", "typings": "taiga-ui-cdk.d.ts", "sideEffects": false}