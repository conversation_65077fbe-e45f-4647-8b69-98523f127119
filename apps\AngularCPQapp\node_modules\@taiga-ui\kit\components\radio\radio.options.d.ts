import { Provider } from '@angular/core';
import { TuiSizeL } from '@taiga-ui/core';
export interface TuiRadioOptions {
    readonly appearances: Readonly<{
        checked: string;
        unchecked: string;
    }>;
    readonly size: TuiSizeL;
}
/** Default values for the checkbox options. */
export declare const TUI_RADIO_DEFAULT_OPTIONS: TuiRadioOptions;
/**
 * Default parameters for Radio component
 */
export declare const TUI_RADIO_OPTIONS: import("@angular/core").InjectionToken<TuiRadioOptions>;
export declare function tuiRadioOptionsProvider(options: Partial<TuiRadioOptions>): Provider;
