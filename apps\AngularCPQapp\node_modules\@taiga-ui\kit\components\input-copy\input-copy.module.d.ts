import * as i0 from "@angular/core";
import * as i1 from "./input-copy.component";
import * as i2 from "./input-copy.directive";
import * as i3 from "@angular/common";
import * as i4 from "@tinkoff/ng-polymorpheus";
import * as i5 from "@taiga-ui/core";
export declare class TuiInputCopyModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputCopyModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputCopyModule, [typeof i1.TuiInputCopyComponent, typeof i2.TuiInputCopyDirective], [typeof i3.CommonModule, typeof i4.PolymorpheusModule, typeof i5.TuiWrapperModule, typeof i5.TuiSvgModule, typeof i5.TuiHintModule, typeof i5.TuiPrimitiveTextfieldModule, typeof i5.TuiAlertModule, typeof i5.TuiTextfieldControllerModule], [typeof i1.TuiInputCopyComponent, typeof i2.TuiInputCopyDirective, typeof i5.TuiTextfieldComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputCopyModule>;
}
