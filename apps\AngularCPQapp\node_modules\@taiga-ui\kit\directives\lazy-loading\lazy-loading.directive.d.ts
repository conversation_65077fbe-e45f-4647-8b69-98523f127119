import { ElementRef } from '@angular/core';
import { SafeResourceUrl } from '@angular/platform-browser';
import { TuiLazyLoadingService } from './lazy-loading.service';
import * as i0 from "@angular/core";
export declare class TuiLazyLoadingDirective {
    private readonly src$;
    private readonly el;
    set srcSetter(src: SafeResourceUrl | string);
    animation: string;
    background: string;
    src: SafeResourceUrl | string | null;
    constructor(src$: TuiLazyLoadingService, el: ElementRef<HTMLImageElement>);
    private get supported();
    onLoad(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiLazyLoadingDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiLazyLoadingDirective, "img[loading=\"lazy\"]", never, { "srcSetter": "src"; }, {}, never>;
}
