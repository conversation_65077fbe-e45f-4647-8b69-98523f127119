import * as i0 from "@angular/core";
import * as i1 from "./components/tree/tree.component";
import * as i2 from "./components/tree-item/tree-item.component";
import * as i3 from "./components/tree-item-content/tree-item-content.component";
import * as i4 from "./directives/tree-children.directive";
import * as i5 from "./directives/tree-item-controller.directive";
import * as i6 from "./directives/tree-controller.directive";
import * as i7 from "./directives/tree-node.directive";
import * as i8 from "@angular/common";
import * as i9 from "@tinkoff/ng-polymorpheus";
import * as i10 from "@taiga-ui/core";
import * as i11 from "@taiga-ui/cdk";
export declare class TuiTreeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTreeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiTreeModule, [typeof i1.TuiTreeComponent, typeof i2.TuiTreeItemComponent, typeof i3.TuiTreeItemContentComponent, typeof i4.TuiTreeChildrenDirective, typeof i5.TuiTreeItemControllerDirective, typeof i6.TuiTreeControllerDirective, typeof i7.TuiTreeNodeDirective], [typeof i8.CommonModule, typeof i9.PolymorpheusModule, typeof i10.TuiExpandModule, typeof i11.TuiLetModule, typeof i10.TuiButtonModule], [typeof i1.TuiTreeComponent, typeof i2.TuiTreeItemComponent, typeof i4.TuiTreeChildrenDirective, typeof i5.TuiTreeItemControllerDirective, typeof i6.TuiTreeControllerDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiTreeModule>;
}
