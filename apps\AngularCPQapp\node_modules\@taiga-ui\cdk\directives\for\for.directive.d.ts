import { OnChanges, TemplateRef, ViewContainerRef } from '@angular/core';
import * as i0 from "@angular/core";
export declare class TuiForDirective<T, K = unknown> implements OnChanges {
    private readonly vcr;
    private ref?;
    ngForOf: T[] | readonly T[] | null;
    ngForElse?: TemplateRef<K>;
    ngForEmpty?: TemplateRef<K>;
    constructor(vcr: ViewContainerRef);
    ngOnChanges(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiForDirective<any, any>, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiForDirective<any, any>, "[ngFor][ngForOf][ngForElse],[ngFor][ngForOf][ngForEmpty]", never, { "ngForOf": "ngForOf"; "ngForElse": "ngForElse"; "ngForEmpty": "ngForEmpty"; }, {}, never>;
}
