import * as i0 from "@angular/core";
import * as i1 from "./radio.component";
import * as i2 from "@angular/common";
import * as i3 from "@taiga-ui/cdk";
import * as i4 from "@taiga-ui/core";
export declare class TuiRadioModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRadioModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiRadioModule, [typeof i1.TuiRadioComponent], [typeof i2.CommonModule, typeof i3.TuiCheckedModule, typeof i3.TuiFocusableModule, typeof i3.TuiFocusedModule, typeof i3.TuiFocusVisibleModule, typeof i4.TuiWrapperModule], [typeof i1.TuiRadioComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiRadioModule>;
}
