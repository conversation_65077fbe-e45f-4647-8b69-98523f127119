import { Provider } from '@angular/core';
import { TuiSizeL } from '@taiga-ui/core';
export interface TuiInputFilesOptions {
    accepts: string;
    /**
     * @description:
     * user - The user-facing camera and/or microphone should be used.
     * environment - The outward-facing camera and/or microphone should be used
     */
    capture: 'environment' | 'user' | null;
    maxFileSize: number;
    multiple: boolean;
    size: TuiSizeL;
}
export declare const TUI_INPUT_FILES_DEFAULT_OPTIONS: TuiInputFilesOptions;
/**
 * Default parameters for input files component
 */
export declare const TUI_INPUT_FILES_OPTIONS: import("@angular/core").InjectionToken<TuiInputFilesOptions>;
export declare function tuiInputFilesOptionsProvider(options: Partial<TuiInputFilesOptions>): Provider;
