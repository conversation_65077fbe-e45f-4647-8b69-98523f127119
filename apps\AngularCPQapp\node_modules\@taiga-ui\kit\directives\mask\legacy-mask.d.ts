/**
 * Copied from
 * {@link https://github.com/text-mask/text-mask/blob/master/angular2/src/angular2TextMask.ts angular2-text-mask}
 * ___
 * "angular2-text-mask" is a legacy not-maintained library. It is published using legacy View Engine distribution.
 * <PERSON><PERSON><PERSON><PERSON> fails to run "View Engine"-libraries in Ivy application.
 * See {@link https://github.com/taiga-family/taiga-ui/issues/2541#issuecomment-1235516443 this comment}.
 */
import { ElementRef, OnChanges, Renderer2 } from '@angular/core';
import { ControlValueAccessor } from '@angular/forms';
import { TuiTextMaskOptions } from '@taiga-ui/core';
import * as i0 from "@angular/core";
/**
 * @internal
 * @deprecated Use {@link https://github.com/taiga-family/maskito Maskito}
 * Don't use it!
 * TODO: delete in v4.0
 */
export declare class MaskedInputDirective implements ControlValueAccessor, OnChanges {
    private _renderer;
    private _elementRef;
    private _compositionMode;
    textMaskConfig: TuiTextMaskOptions;
    onChange: (_: any) => void;
    onTouched: () => void;
    private textMaskInputElement;
    private inputElement;
    /** Whether the user is creating a composition string (IME events). */
    private _composing;
    constructor(_renderer: Renderer2, _elementRef: ElementRef, _compositionMode: boolean);
    ngOnChanges(): void;
    writeValue(value: any): void;
    registerOnChange(fn: (_: any) => void): void;
    registerOnTouched(fn: () => void): void;
    setDisabledState(isDisabled: boolean): void;
    _handleInput(value: any): void;
    _setupMask(create?: boolean): void;
    _compositionStart(): void;
    _compositionEnd(value: any): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<MaskedInputDirective, [null, null, { optional: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<MaskedInputDirective, "[textMask]", ["textMask"], { "textMaskConfig": "textMask"; }, {}, never>;
}
/**
 * @internal
 * @deprecated Use {@link https://github.com/taiga-family/maskito Maskito}
 * Don't use it!
 * TODO: delete in v4.0
 */
export declare class TextMaskModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TextMaskModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TextMaskModule, [typeof MaskedInputDirective], never, [typeof MaskedInputDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TextMaskModule>;
}
