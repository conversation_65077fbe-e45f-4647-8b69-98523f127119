/**
 * {@link https://unicode-table.com/en/00A0/ Non-breaking space}.
 */
export const CHAR_NO_BREAK_SPACE = '\u00A0';
/**
 * {@link https://unicode-table.com/en/2013/ EN dash}
 * is used to indicate a range of numbers or a span of time.
 * @example 2006–2022
 * ___
 * Don't confuse with {@link CHAR_EM_DASH} or {@link CHAR_HYPHEN}!
 */
export const CHAR_EN_DASH = '\u2013';
/**
 * {@link https://unicode-table.com/en/2014/ EM dash}
 * is used to mark a break in a sentence.
 * @example Taiga UI — powerful set of open source components for Angular
 * ___
 * Don't confuse with {@link CHAR_EN_DASH} or {@link CHAR_HYPHEN}!
 */
export const CHAR_EM_DASH = '\u2014';
/**
 * {@link https://unicode-table.com/en/00AB/ Left-Pointing Double Angle Quotation Mark}
 */
export const CHAR_LAQUO = '\u00AB';
/**
 * {@link https://unicode-table.com/en/00BB/ Right-Pointing Double Angle Quotation Mark}
 */
export const CHAR_RAQUO = '\u00BB';
/**
 * {@link https://unicode-table.com/en/002D/ Hyphen (minus sign)}
 * is used to combine words.
 * @example well-behaved
 * ___
 * Don't confuse with {@link CHAR_EN_DASH} or {@link CHAR_EM_DASH}!
 */
export const CHAR_HYPHEN = '\u002D';
/**
 * {@link https://unicode-table.com/en/2212/ Minus}
 * is used as math operator symbol or before negative digits.
 * ---
 * Can be used as `&minus;`. Don't confuse with {@link CHAR_HYPHEN}
 */
export const CHAR_MINUS = '\u2212';
/**
 * {@link https://unicode-table.com/en/002B/ Plus}
 */
export const CHAR_PLUS = '\u002B';
/**
 * {@link https://unicode-table.com/en/2022/ Bullet}.
 */
export const CHAR_BULLET = '\u2022';
/**
 * {@link https://unicode-table.com/en/2026/ Suspension points}.
 */
export const CHAR_ELLIPSIS = '\u2026';
/**
 * {@link https://unicode-table.com/en/00A4/ Suspension points}.
 */
export const CHAR_CURRENCY_SIGN = '\u00A4';
/**
 * {@link https://unicode-table.com/en/200b/ Suspension points}.
 */
export const CHAR_ZERO_WIDTH_SPACE = '\u200B';
//# sourceMappingURL=data:application/json;base64,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