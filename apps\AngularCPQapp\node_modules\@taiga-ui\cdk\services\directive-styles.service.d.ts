import { ComponentFactoryResolver, Injector, OnD<PERSON>roy, Type } from '@angular/core';
import * as i0 from "@angular/core";
/**
 * Service to use styles with directives
 */
export declare class TuiDirectiveStylesService implements OnDestroy {
    private readonly resolver;
    private readonly injector;
    private readonly map;
    constructor(resolver: ComponentFactoryResolver, injector: Injector);
    addComponent(component: Type<unknown>): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiDirectiveStylesService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TuiDirectiveStylesService>;
}
