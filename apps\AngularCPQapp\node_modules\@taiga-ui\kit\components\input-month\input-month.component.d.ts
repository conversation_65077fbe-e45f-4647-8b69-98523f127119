import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiNullableControl, TuiBooleanHandler, TuiFocusableElementAccessor, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>i<PERSON>ear } from '@taiga-ui/cdk';
import { TuiSizeL, TuiSizeS, TuiTextfieldSizeDirective, TuiWithOptionalMinMax } from '@taiga-ui/core';
import { TuiInputDateOptions } from '@taiga-ui/kit/tokens';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiInputMonthComponent extends AbstractTuiNullableControl<TuiMonth> implements TuiWithOptionalMinMax<TuiMonth>, TuiFocusableElementAccessor {
    readonly formatter: TuiHandler<TuiMonth | null, Observable<string>>;
    private readonly isMobile;
    private readonly options;
    private readonly textfieldSize;
    private readonly textfield?;
    min: Tui<PERSON><PERSON>h | null;
    max: TuiMonth | null;
    disabledItemHandler: TuiBooleanHandler<TuiMonth>;
    defaultActiveYear: TuiYear;
    activeYear?: TuiYear;
    open: boolean;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, formatter: TuiHandler<TuiMonth | null, Observable<string>>, isMobile: boolean, options: TuiInputDateOptions, textfieldSize: TuiTextfieldSizeDirective);
    get size(): TuiSizeL | TuiSizeS;
    get computedMin(): TuiMonth;
    get computedMax(): TuiMonth;
    get nativeFocusableElement(): HTMLInputElement | null;
    get computedDefaultActiveYear(): TuiYear;
    get focused(): boolean;
    get calendarIcon(): TuiInputDateOptions['icon'];
    get nativePicker(): boolean;
    get nativePickerMin(): string;
    get nativePickerMax(): string;
    get nativeValue(): string;
    onNativeChange(value: string): void;
    onValueChange(value: string): void;
    onMonthClick(month: TuiMonth): void;
    onFocused(focused: boolean): void;
    onOpenChange(open: boolean): void;
    setDisabledState(): void;
    private close;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputMonthComponent, [{ optional: true; self: true; }, null, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputMonthComponent, "tui-input-month", never, { "min": "min"; "max": "max"; "disabledItemHandler": "disabledItemHandler"; "defaultActiveYear": "defaultActiveYear"; }, {}, never, ["*", "input"]>;
}
