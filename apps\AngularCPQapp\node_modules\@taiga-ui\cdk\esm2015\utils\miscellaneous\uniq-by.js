export function tuiUniqBy(array, key) {
    return Array.from(array
        .reduce((map, item) => (map.has(item[key]) ? map : map.set(item[key], item)), new Map())
        .values());
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidW5pcS1ieS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uL3Byb2plY3RzL2Nkay91dGlscy9taXNjZWxsYW5lb3VzL3VuaXEtYnkudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBRUEsTUFBTSxVQUFVLFNBQVMsQ0FDckIsS0FBbUIsRUFDbkIsR0FBWTtJQUVaLE9BQU8sS0FBSyxDQUFDLElBQUksQ0FDYixLQUFLO1NBQ0EsTUFBTSxDQUNILENBQUMsR0FBRyxFQUFFLElBQUksRUFBRSxFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFLElBQUksQ0FBQyxDQUFDLEVBQ3BFLElBQUksR0FBRyxFQUFxQixDQUMvQjtTQUNBLE1BQU0sRUFBRSxDQUNoQixDQUFDO0FBQ04sQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7VHVpVmFsdWVzT2Z9IGZyb20gJ0B0YWlnYS11aS9jZGsvdHlwZXMnO1xuXG5leHBvcnQgZnVuY3Rpb24gdHVpVW5pcUJ5PFQgZXh0ZW5kcyBSZWNvcmQ8c3RyaW5nLCBhbnk+PihcbiAgICBhcnJheTogcmVhZG9ubHkgVFtdLFxuICAgIGtleToga2V5b2YgVCxcbik6IHJlYWRvbmx5IFRbXSB7XG4gICAgcmV0dXJuIEFycmF5LmZyb20oXG4gICAgICAgIGFycmF5XG4gICAgICAgICAgICAucmVkdWNlKFxuICAgICAgICAgICAgICAgIChtYXAsIGl0ZW0pID0+IChtYXAuaGFzKGl0ZW1ba2V5XSkgPyBtYXAgOiBtYXAuc2V0KGl0ZW1ba2V5XSwgaXRlbSkpLFxuICAgICAgICAgICAgICAgIG5ldyBNYXA8VHVpVmFsdWVzT2Y8VD4sIFQ+KCksXG4gICAgICAgICAgICApXG4gICAgICAgICAgICAudmFsdWVzKCksXG4gICAgKTtcbn1cbiJdfQ==