import * as i0 from "@angular/core";
import * as i1 from "./slider.component";
import * as i2 from "./helpers/slider-thumb-label/slider-thumb-label.component";
import * as i3 from "./helpers/slider-key-steps.directive";
import * as i4 from "./helpers/slider-readonly.directive";
import * as i5 from "@angular/common";
export declare class TuiSliderModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSliderModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiSliderModule, [typeof i1.TuiSliderComponent, typeof i2.TuiSliderThumbLabelComponent, typeof i3.TuiSliderKeyStepsDirective, typeof i4.TuiSliderReadonlyDirective], [typeof i5.CommonModule], [typeof i1.TuiSliderComponent, typeof i2.TuiSliderThumbLabelComponent, typeof i3.TuiSliderKeyStepsDirective, typeof i4.TuiSliderReadonlyDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiSliderModule>;
}
