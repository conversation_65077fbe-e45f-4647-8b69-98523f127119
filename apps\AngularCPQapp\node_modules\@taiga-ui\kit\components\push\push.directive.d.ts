import { TuiButtonOptions, TuiModeDirective } from '@taiga-ui/core';
import { Subject } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiPushDirective extends TuiModeDirective implements TuiButtonOptions {
    private readonly modeDirective;
    size: TuiButtonOptions['size'];
    shape: null;
    readonly change$: Subject<void>;
    constructor(modeDirective: TuiModeDirective | null);
    get appearance(): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPushDirective, [{ optional: true; skipSelf: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiPushDirective, "tui-push", never, {}, {}, never>;
}
