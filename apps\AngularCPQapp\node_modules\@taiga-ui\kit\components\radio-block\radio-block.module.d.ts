import * as i0 from "@angular/core";
import * as i1 from "./radio-block.component";
import * as i2 from "@angular/common";
import * as i3 from "@angular/forms";
import * as i4 from "@taiga-ui/kit/components/radio";
import * as i5 from "@taiga-ui/core";
export declare class TuiRadioBlockModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRadioBlockModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiRadioBlockModule, [typeof i1.TuiRadioBlockComponent], [typeof i2.CommonModule, typeof i3.FormsModule, typeof i4.TuiRadioModule, typeof i5.TuiWrapperModule], [typeof i1.TuiRadioBlockComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiRadioBlockModule>;
}
