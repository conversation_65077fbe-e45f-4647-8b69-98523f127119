import { Provider } from '@angular/core';
import { TuiContextWithImplicit } from '@taiga-ui/cdk';
import { TuiSizeL, TuiSizeS } from '@taiga-ui/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
export interface TuiInputPasswordOptions {
    readonly icons: Readonly<{
        hide: PolymorpheusContent<TuiContextWithImplicit<TuiSizeL | TuiSizeS>>;
        show: PolymorpheusContent<TuiContextWithImplicit<TuiSizeL | TuiSizeS>>;
    }>;
}
/** Default values for the input password options. */
export declare const TUI_INPUT_PASSWORD_DEFAULT_OPTIONS: TuiInputPasswordOptions;
/**
 * Default parameters for input password component
 */
export declare const TUI_INPUT_PASSWORD_OPTIONS: import("@angular/core").InjectionToken<TuiInputPasswordOptions>;
export declare function tuiInputPasswordOptionsProvider(options: Partial<TuiInputPasswordOptions>): Provider;
