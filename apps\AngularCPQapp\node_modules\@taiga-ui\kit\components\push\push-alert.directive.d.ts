import { ChangeDetectorRef, TemplateRef } from '@angular/core';
import { PolymorpheusTemplate } from '@tinkoff/ng-polymorpheus';
import { Observable } from 'rxjs';
import { TuiPushService } from './push.service';
import * as i0 from "@angular/core";
export declare class TuiPushAlertDirective extends PolymorpheusTemplate {
    private readonly show$;
    set tuiPush(show: boolean);
    constructor(template: TemplateRef<any>, cdr: ChangeDetectorRef, destroy$: Observable<unknown>, push: TuiPushService);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPushAlertDirective, [null, null, { self: true; }, null]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiPushAlertDirective, "[tuiPush]", never, { "tuiPush": "tuiPush"; }, {}, never>;
}
