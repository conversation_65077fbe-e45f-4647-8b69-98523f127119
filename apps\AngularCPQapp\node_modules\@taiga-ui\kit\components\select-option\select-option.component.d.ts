import { <PERSON><PERSON><PERSON><PERSON>, ElementRef, OnInit, TemplateRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TuiContextWithImplicit, TuiIdentityMatcher } from '@taiga-ui/cdk';
import { TuiCommonIcons, TuiDataListComponent, TuiDataListHost, TuiOptionComponent } from '@taiga-ui/core';
import { PolymorpheusComponent } from '@tinkoff/ng-polymorpheus';
import * as i0 from "@angular/core";
export declare class TuiSelectOptionComponent<T> implements OnInit, DoCheck {
    readonly icons: TuiCommonIcons;
    readonly context: TuiContextWithImplicit<TemplateRef<Record<string, unknown>>>;
    private readonly host;
    private readonly el;
    protected readonly option: TuiOptionComponent<T>;
    protected readonly dataList: TuiDataListComponent<T> | null;
    protected readonly control: NgControl;
    protected readonly abstractControl: AbstractTuiControl<T> | null;
    private readonly changeDetection$;
    readonly selected$: import("rxjs").Observable<boolean>;
    constructor(icons: TuiCommonIcons, context: TuiContextWithImplicit<TemplateRef<Record<string, unknown>>>, host: TuiDataListHost<T>, el: ElementRef<HTMLElement>, option: TuiOptionComponent<T>, dataList: TuiDataListComponent<T> | null, control: NgControl, abstractControl: AbstractTuiControl<T> | null);
    get matcher(): TuiIdentityMatcher<T>;
    ngOnInit(): void;
    ngDoCheck(): void;
    protected get value(): T | null;
    protected get selected(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSelectOptionComponent<any>, [null, null, null, null, null, { optional: true; }, null, { optional: true; }]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiSelectOptionComponent<any>, "tui-select-option", never, {}, {}, never, never>;
}
export declare const TUI_SELECT_OPTION: PolymorpheusComponent<TuiSelectOptionComponent<unknown>, any>;
