import { ElementRef, OnChanges, Renderer2 } from '@angular/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiHighlightDirective implements OnChanges {
    private readonly doc;
    private readonly el;
    private readonly renderer;
    private readonly highlight;
    private readonly treeWalker;
    tuiHighlight: string;
    tuiHighlightColor: string;
    constructor(doc: Document, el: ElementRef<HTMLElement>, renderer: Renderer2, resize$: Observable<unknown>);
    get match(): boolean;
    ngOnChanges(): void;
    private updateStyles;
    private indexOf;
    private setUpHighlight;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiHighlightDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiHighlightDirective, "[tuiHighlight]", never, { "tuiHighlight": "tuiHighlight"; "tuiHighlightColor": "tuiHighlightColor"; }, {}, never>;
}
