import { Provider } from '@angular/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
export interface TuiInputCountOptions {
    readonly appearance: string;
    readonly hideButtons: boolean;
    readonly icons: Readonly<{
        down: PolymorpheusContent<Record<string, unknown>>;
        up: <PERSON>ymorpheusContent<Record<string, unknown>>;
    }>;
    readonly max: number;
    readonly min: number;
    readonly postfix: string;
    readonly step: number;
}
/** Default values for the input count options. */
export declare const TUI_INPUT_COUNT_DEFAULT_OPTIONS: TuiInputCountOptions;
/**
 * Default parameters for input count component
 */
export declare const TUI_INPUT_COUNT_OPTIONS: import("@angular/core").InjectionToken<TuiInputCountOptions>;
export declare function tuiInputCountOptionsProvider(options: Partial<TuiInputCountOptions>): Provider;
