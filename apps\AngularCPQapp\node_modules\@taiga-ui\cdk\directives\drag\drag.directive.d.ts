import { ElementRef } from '@angular/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
/**
 * @deprecated not used anywhere
 */
export declare class TuiDragDirective {
    private readonly el;
    private readonly dragAndDropFrom$;
    readonly start: Observable<MouseEvent>;
    readonly continues: Observable<MouseEvent>;
    readonly end: Observable<MouseEvent>;
    constructor(el: ElementRef<Element>);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiDragDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiDragDirective, "[tuiDragStart], [tuiDragContinues], [tuiDragEnd]", never, {}, { "start": "tuiDragStart"; "continues": "tuiDragContinues"; "end": "tuiDragEnd"; }, never>;
}
