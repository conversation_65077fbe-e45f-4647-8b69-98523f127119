import * as i0 from "@angular/core";
import * as i1 from "./select.component";
import * as i2 from "./select.directive";
import * as i3 from "./native-select/native-select.component";
import * as i4 from "./native-select/native-select-group.component";
import * as i5 from "@angular/common";
import * as i6 from "@tinkoff/ng-polymorpheus";
import * as i7 from "@taiga-ui/cdk";
import * as i8 from "@taiga-ui/core";
import * as i9 from "@taiga-ui/kit/components/select-option";
import * as i10 from "@taiga-ui/kit/components/arrow";
import * as i11 from "@taiga-ui/kit/components/data-list-wrapper";
import * as i12 from "@taiga-ui/kit/pipes";
export declare class TuiSelectModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSelectModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiSelectModule, [typeof i1.TuiSelectComponent, typeof i2.TuiSelectDirective, typeof i3.TuiNativeSelectComponent, typeof i4.TuiNativeSelectGroupComponent], [typeof i5.CommonModule, typeof i6.PolymorpheusModule, typeof i7.TuiActiveZoneModule, typeof i8.TuiPrimitiveTextfieldModule, typeof i8.TuiHostedDropdownModule, typeof i9.TuiSelectOptionModule, typeof i10.TuiArrowModule, typeof i8.TuiWrapperModule, typeof i8.TuiTextfieldControllerModule, typeof i11.TuiDataListWrapperModule, typeof i8.TuiDataListModule, typeof i12.TuiStringifyContentPipeModule], [typeof i1.TuiSelectComponent, typeof i2.TuiSelectDirective, typeof i8.TuiTextfieldComponent, typeof i3.TuiNativeSelectComponent, typeof i4.TuiNativeSelectGroupComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiSelectModule>;
}
