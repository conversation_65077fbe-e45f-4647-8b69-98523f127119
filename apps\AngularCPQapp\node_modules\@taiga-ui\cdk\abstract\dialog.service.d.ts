import { TuiBaseDialogContext } from '@taiga-ui/cdk/interfaces';
import { TuiIdService } from '@taiga-ui/cdk/services';
import { TuiDialog } from '@taiga-ui/cdk/types';
import { PolymorpheusComponent, PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { BehaviorSubject, Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare abstract class AbstractTuiDialogService<T, K = void> extends Observable<ReadonlyArray<TuiDialog<T, any>>> {
    private readonly idService;
    protected abstract readonly component: PolymorpheusComponent<any, TuiDialog<T, any>>;
    protected abstract readonly defaultOptions: T;
    protected readonly dialogs$: BehaviorSubject<readonly TuiDialog<T, any>[]>;
    constructor(idService: TuiIdService);
    open<G = void>(content: PolymorpheusContent<T & TuiBaseDialogContext<K extends void ? G : K>>, options?: Partial<T>): Observable<K extends void ? G : K>;
    static ɵfac: i0.ɵɵFactoryDeclaration<AbstractTuiDialogService<any, any>, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<AbstractTuiDialogService<any, any>>;
}
