import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
/**
 * Directive to imitate :focus-visible
 * (https://developer.mozilla.org/en-US/docs/Web/CSS/:focus-visible)
 * in browsers that do not support it
 */
export declare class TuiFocusVisibleDirective {
    readonly tuiFocusVisibleChange: Observable<boolean>;
    constructor(tuiFocusVisibleChange: Observable<boolean>);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiFocusVisibleDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiFocusVisibleDirective, "[tuiFocusVisibleChange]", never, {}, { "tuiFocusVisibleChange": "tuiFocusVisibleChange"; }, never>;
}
