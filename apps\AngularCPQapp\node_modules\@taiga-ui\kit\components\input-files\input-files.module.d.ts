import * as i0 from "@angular/core";
import * as i1 from "./input-files.component";
import * as i2 from "./input-files.directive";
import * as i3 from "./max-size-rejection-error.pipe";
import * as i4 from "@angular/common";
import * as i5 from "@tinkoff/ng-polymorpheus";
import * as i6 from "@taiga-ui/cdk";
import * as i7 from "@taiga-ui/core";
import * as i8 from "@taiga-ui/kit/components/files";
export declare class TuiInputFilesModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputFilesModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputFilesModule, [typeof i1.TuiInputFilesComponent, typeof i2.TuiInputFilesDirective, typeof i3.TuiMaxSizeRejectionErrorPipe], [typeof i4.CommonModule, typeof i5.PolymorpheusModule, typeof i6.TuiLetModule, typeof i6.TuiFocusedModule, typeof i6.TuiFocusVisibleModule, typeof i6.TuiPressedModule, typeof i6.TuiHoveredModule, typeof i6.TuiFocusableModule, typeof i6.TuiDroppableModule, typeof i7.TuiWrapperModule, typeof i7.TuiSvgModule, typeof i7.TuiLinkModule, typeof i7.TuiLoaderModule, typeof i7.TuiButtonModule, typeof i7.TuiGroupModule, typeof i8.TuiFilesModule], [typeof i1.TuiInputFilesComponent, typeof i2.TuiInputFilesDirective, typeof i8.TuiFilesComponent, typeof i8.TuiFileComponent, typeof i6.TuiItemDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputFilesModule>;
}
