import * as i0 from "@angular/core";
import * as i1 from "./rating.component";
import * as i2 from "@taiga-ui/core";
import * as i3 from "@taiga-ui/cdk";
import * as i4 from "@angular/forms";
export declare class TuiRatingModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRatingModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiRatingModule, [typeof i1.TuiRatingComponent], [typeof i2.TuiSvgModule, typeof i3.TuiFocusableModule, typeof i3.TuiRepeatTimesModule, typeof i4.FormsModule, typeof i4.ReactiveFormsModule], [typeof i1.TuiRatingComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiRatingModule>;
}
