import { ALWAYS_TRUE_HANDLER } from '@taiga-ui/cdk/constants';
import { tuiCreateTokenFromFactory } from '@taiga-ui/cdk/utils';
/**
 * @description:
 * The isTrusted read-only property of the Event interface is a boolean value that is true
 * when the event was generated by a user action, and false when the event was created or
 * modified by a script or dispatched via EventTarget.dispatchEvent().
 */
export const TUI_TAKE_ONLY_TRUSTED_EVENTS = tuiCreateTokenFromFactory(ALWAYS_TRUE_HANDLER);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGFrZS1vbmx5LXRydXN0ZWQtZXZlbnRzLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vcHJvamVjdHMvY2RrL3Rva2Vucy90YWtlLW9ubHktdHJ1c3RlZC1ldmVudHMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsT0FBTyxFQUFDLG1CQUFtQixFQUFDLE1BQU0seUJBQXlCLENBQUM7QUFDNUQsT0FBTyxFQUFDLHlCQUF5QixFQUFDLE1BQU0scUJBQXFCLENBQUM7QUFFOUQ7Ozs7O0dBS0c7QUFDSCxNQUFNLENBQUMsTUFBTSw0QkFBNEIsR0FDckMseUJBQXlCLENBQVUsbUJBQW1CLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7QUxXQVlTX1RSVUVfSEFORExFUn0gZnJvbSAnQHRhaWdhLXVpL2Nkay9jb25zdGFudHMnO1xuaW1wb3J0IHt0dWlDcmVhdGVUb2tlbkZyb21GYWN0b3J5fSBmcm9tICdAdGFpZ2EtdWkvY2RrL3V0aWxzJztcblxuLyoqXG4gKiBAZGVzY3JpcHRpb246XG4gKiBUaGUgaXNUcnVzdGVkIHJlYWQtb25seSBwcm9wZXJ0eSBvZiB0aGUgRXZlbnQgaW50ZXJmYWNlIGlzIGEgYm9vbGVhbiB2YWx1ZSB0aGF0IGlzIHRydWVcbiAqIHdoZW4gdGhlIGV2ZW50IHdhcyBnZW5lcmF0ZWQgYnkgYSB1c2VyIGFjdGlvbiwgYW5kIGZhbHNlIHdoZW4gdGhlIGV2ZW50IHdhcyBjcmVhdGVkIG9yXG4gKiBtb2RpZmllZCBieSBhIHNjcmlwdCBvciBkaXNwYXRjaGVkIHZpYSBFdmVudFRhcmdldC5kaXNwYXRjaEV2ZW50KCkuXG4gKi9cbmV4cG9ydCBjb25zdCBUVUlfVEFLRV9PTkxZX1RSVVNURURfRVZFTlRTID1cbiAgICB0dWlDcmVhdGVUb2tlbkZyb21GYWN0b3J5PGJvb2xlYW4+KEFMV0FZU19UUlVFX0hBTkRMRVIpO1xuIl19