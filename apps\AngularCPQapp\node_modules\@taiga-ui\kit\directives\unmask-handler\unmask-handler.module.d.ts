import * as i0 from "@angular/core";
import * as i1 from "./unmask-handler.directive";
export declare class TuiUnmaskHandlerModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiUnmaskHandlerModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiUnmaskHandlerModule, [typeof i1.TuiUnmaskHandlerDirective], never, [typeof i1.TuiUnmaskHandlerDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiUnmaskHandlerModule>;
}
