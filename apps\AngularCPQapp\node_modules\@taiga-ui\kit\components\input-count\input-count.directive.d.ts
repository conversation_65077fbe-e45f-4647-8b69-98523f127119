import { AfterViewInit } from '@angular/core';
import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { TuiInputCountComponent } from './input-count.component';
import * as i0 from "@angular/core";
export declare class TuiInputCountDirective extends AbstractTuiTextfieldHost<TuiInputCountComponent> implements AfterViewInit {
    onValueChange(value: string): void;
    ngAfterViewInit(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputCountDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputCountDirective, "tui-input-count", never, {}, {}, never>;
}
