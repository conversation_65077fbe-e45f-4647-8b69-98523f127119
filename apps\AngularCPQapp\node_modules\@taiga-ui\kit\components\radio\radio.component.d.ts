import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiNullableControl, TuiFocusableElementAccessor, TuiIdentityMatcher, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import { TuiSizeL } from '@taiga-ui/core';
import { TuiRadioGroupComponent } from '@taiga-ui/kit/components/radio-group';
import { TuiRadioOptions } from './radio.options';
import * as i0 from "@angular/core";
export declare class TuiRadioComponent<T> extends AbstractTuiNullableControl<T> implements TuiFocusableElementAccessor {
    private readonly options;
    private readonly radioGroup;
    private readonly focusableElement?;
    item?: T | null;
    identityMatcher: TuiIdentityMatcher<T>;
    name: string | null;
    size: TuiSizeL;
    pseudoDisabled: boolean;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, options: TuiRadioOptions, radioGroup: TuiRadioGroupComponent | null);
    get appearance(): string;
    get computedDisabled(): boolean;
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    get checked(): boolean;
    get computedName(): string;
    get isFocusable(): boolean;
    onChecked(checked: boolean): void;
    onFocused(focused: boolean): void;
    onFocusVisible(focusVisible: boolean): void;
    private get radioGroupName();
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRadioComponent<any>, [{ optional: true; self: true; }, null, null, { optional: true; }]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiRadioComponent<any>, "tui-radio", never, { "item": "item"; "identityMatcher": "identityMatcher"; "name": "name"; "size": "size"; "pseudoDisabled": "pseudoDisabled"; }, {}, never, never>;
}
