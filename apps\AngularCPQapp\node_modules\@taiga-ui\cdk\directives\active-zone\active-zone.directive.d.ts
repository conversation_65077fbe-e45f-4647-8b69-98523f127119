import { ElementRef, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiActiveZoneDirective implements OnDestroy {
    private readonly active$;
    private readonly zone;
    private readonly el;
    private readonly directParentActiveZone;
    private subActiveZones;
    private tuiActiveZoneParent;
    set tuiActiveZoneParentSetter(zone: TuiActiveZoneDirective | null);
    readonly tuiActiveZoneChange: Observable<boolean>;
    constructor(active$: Observable<Element | null>, zone: NgZone, el: ElementRef<Element>, directParentActiveZone: TuiActiveZoneDirective | null);
    ngOnDestroy(): void;
    contains(node: Node): boolean;
    private setZone;
    private addSubActiveZone;
    private removeSubActiveZone;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiActiveZoneDirective, [null, null, null, { optional: true; skipSelf: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiActiveZoneDirective, "[tuiActiveZone]:not(ng-container), [tuiActiveZoneChange]:not(ng-container), [tuiActiveZoneParent]:not(ng-container)", ["tuiActiveZone"], { "tuiActiveZoneParentSetter": "tuiActiveZoneParent"; }, { "tuiActiveZoneChange": "tuiActiveZoneChange"; }, never>;
}
