import { ChangeDetectorRef, ElementRef, Injector } from '@angular/core';
import { NgControl } from '@angular/forms';
import { TuiSizeS } from '@taiga-ui/core';
import { TuiSliderOptions } from './slider.options';
import * as i0 from "@angular/core";
export declare class TuiSliderComponent {
    private readonly control;
    readonly options: TuiSliderOptions;
    readonly el: ElementRef<HTMLInputElement>;
    private readonly userAgent;
    private readonly injector;
    size: TuiSizeS;
    segments: number;
    get min(): number;
    get max(): number;
    get step(): number;
    get value(): number;
    set value(newValue: number);
    get valueRatio(): number;
    get valuePercentage(): number;
    get segmentWidth(): number;
    get isOldEdge(): boolean;
    get hasKeySteps(): boolean;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, options: TuiSliderOptions, el: ElementRef<HTMLInputElement>, userAgent: string, injector: Injector);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSliderComponent, [{ optional: true; self: true; }, null, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiSliderComponent, "input[type=range][tuiSlider]", never, { "size": "size"; "segments": "segments"; }, {}, never, never>;
}
