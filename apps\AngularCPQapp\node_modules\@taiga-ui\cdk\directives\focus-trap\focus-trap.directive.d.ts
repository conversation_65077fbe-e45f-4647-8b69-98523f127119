import { Element<PERSON><PERSON>, On<PERSON><PERSON>roy, Renderer2 } from '@angular/core';
import * as i0 from "@angular/core";
export declare class TuiFocusTrapDirective implements OnDestroy {
    private readonly doc;
    private readonly el;
    private readonly renderer;
    private readonly activeElement;
    constructor(doc: Document, el: ElementRef<HTMLElement>, renderer: Renderer2);
    onBlur(): void;
    onFocusIn(node: Node): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiFocusTrapDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiFocusTrapDirective, "[tuiFocusTrap]", never, {}, {}, never>;
}
