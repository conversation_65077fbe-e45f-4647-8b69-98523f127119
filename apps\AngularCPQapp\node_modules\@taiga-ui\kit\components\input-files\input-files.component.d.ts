import { ChangeDetectorRef, EventEmitter, TemplateRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiNullableControl, TuiContextWithImplicit, TuiFocusableElementAccessor, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import { TuiSizeL } from '@taiga-ui/core';
import { TuiFileLike } from '@taiga-ui/kit/interfaces';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { Observable } from 'rxjs';
import { TuiInputFilesDirective } from './input-files.directive';
import { TuiInputFilesOptions } from './input-files.options';
import * as i0 from "@angular/core";
export declare class TuiInputFilesComponent extends AbstractTuiNullableControl<TuiFileLike | readonly TuiFileLike[]> implements TuiFocusableElementAccessor {
    readonly isMobile: boolean;
    readonly inputFileTexts$: Observable<Record<'defaultLabelMultiple' | 'defaultLabelSingle' | 'defaultLinkMultiple' | 'defaultLinkSingle' | 'drop' | 'dropMultiple' | 'formatRejectionReason' | 'maxSizeRejectionReason', string>>;
    readonly options: TuiInputFilesOptions;
    private readonly input?;
    private files?;
    readonly nativeInput?: TuiInputFilesDirective;
    readonly template?: TemplateRef<TuiContextWithImplicit<boolean>>;
    readonly formatRejection: PolymorpheusContent;
    readonly maxSizeRejection: PolymorpheusContent;
    link: PolymorpheusContent;
    label: PolymorpheusContent;
    /**
     * @deprecated: use `<input tuiInputFiles accept="image/*" />`
     */
    accept: string;
    /**
     * @deprecated: use `<input tuiInputFiles multiple />`
     */
    multiple: boolean;
    size: TuiSizeL;
    maxFileSize: number;
    readonly reject: EventEmitter<TuiFileLike | readonly TuiFileLike[]>;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, isMobile: boolean, inputFileTexts$: Observable<Record<'defaultLabelMultiple' | 'defaultLabelSingle' | 'defaultLinkMultiple' | 'defaultLinkSingle' | 'drop' | 'dropMultiple' | 'formatRejectionReason' | 'maxSizeRejectionReason', string>>, options: TuiInputFilesOptions);
    get computedMultiple(): boolean;
    get computedAccept(): string;
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    get computedPseudoHovered(): boolean | null;
    get computedLink$(): Observable<PolymorpheusContent>;
    get computedLabel$(): Observable<PolymorpheusContent>;
    get fileDragged(): boolean;
    get arrayValue(): readonly TuiFileLike[];
    onFocused(focused: boolean): void;
    onFilesSelected(): void;
    onDropped(event: DataTransfer): void;
    onDragOver(dataTransfer: DataTransfer | null): void;
    removeFile(removedFile: TuiFileLike): void;
    private computeLink$;
    private computeLabel$;
    private getValueArray;
    private processSelectedFiles;
    private isFormatAcceptable;
    private rejectFiles;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputFilesComponent, [{ optional: true; self: true; }, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputFilesComponent, "tui-input-files", never, { "link": "link"; "label": "label"; "accept": "accept"; "multiple": "multiple"; "size": "size"; "maxFileSize": "maxFileSize"; }, { "reject": "reject"; }, ["nativeInput", "template"], ["input"]>;
}
