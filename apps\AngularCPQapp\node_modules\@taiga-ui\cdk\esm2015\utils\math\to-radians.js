/**
 * Converts angle in degrees to radians
 */
export function tuiToRadians(deg) {
    return (deg * Math.PI) / 180;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidG8tcmFkaWFucy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uL3Byb2plY3RzL2Nkay91dGlscy9tYXRoL3RvLXJhZGlhbnMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7O0dBRUc7QUFDSCxNQUFNLFVBQVUsWUFBWSxDQUFDLEdBQVc7SUFDcEMsT0FBTyxDQUFDLEdBQUcsR0FBRyxJQUFJLENBQUMsRUFBRSxDQUFDLEdBQUcsR0FBRyxDQUFDO0FBQ2pDLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvbnZlcnRzIGFuZ2xlIGluIGRlZ3JlZXMgdG8gcmFkaWFuc1xuICovXG5leHBvcnQgZnVuY3Rpb24gdHVpVG9SYWRpYW5zKGRlZzogbnVtYmVyKTogbnVtYmVyIHtcbiAgICByZXR1cm4gKGRlZyAqIE1hdGguUEkpIC8gMTgwO1xufVxuIl19