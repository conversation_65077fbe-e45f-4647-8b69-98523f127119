import * as i0 from "@angular/core";
import * as i1 from "./select-option.component";
import * as i2 from "@angular/common";
import * as i3 from "@taiga-ui/core";
export declare class TuiSelectOptionModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSelectOptionModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiSelectOptionModule, [typeof i1.TuiSelectOptionComponent], [typeof i2.CommonModule, typeof i3.TuiSvgModule, typeof i3.TuiScrollIntoViewModule], [typeof i1.TuiSelectOptionComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiSelectOptionModule>;
}
