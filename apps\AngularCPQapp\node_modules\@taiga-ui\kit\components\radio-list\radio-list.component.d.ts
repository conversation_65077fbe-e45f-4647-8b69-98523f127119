import { ChangeDetectorRef, ElementRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiNullableControl, TuiBooleanHandler, TuiIdentityMatcher, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import { TuiOrientation, TuiSizeL, TuiValueContentContext } from '@taiga-ui/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import * as i0 from "@angular/core";
export declare class TuiRadioListComponent<T> extends AbstractTuiNullableControl<T> {
    private readonly el;
    private readonly radioButtons;
    items: readonly T[];
    size: TuiSizeL;
    identityMatcher: TuiIdentityMatcher<T>;
    orientation: TuiOrientation;
    disabledItemHandler: TuiBooleanHandler<T>;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, el: ElementRef<HTMLElement>);
    itemContent: PolymorpheusContent<TuiValueContentContext<T> & {
        index: number;
    }>;
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    computeId(index: number): string;
    itemIsDisabled(item: T): boolean;
    /** @deprecated use 'value' setter */
    onModelChange(value: T): void;
    itemIsActive(item: T): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRadioListComponent<any>, [{ optional: true; self: true; }, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiRadioListComponent<any>, "tui-radio-list", never, { "items": "items"; "size": "size"; "identityMatcher": "identityMatcher"; "orientation": "orientation"; "disabledItemHandler": "disabledItemHandler"; "itemContent": "itemContent"; }, {}, never, never>;
}
