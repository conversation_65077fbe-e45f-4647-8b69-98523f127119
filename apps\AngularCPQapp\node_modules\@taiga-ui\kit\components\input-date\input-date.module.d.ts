import * as i0 from "@angular/core";
import * as i1 from "./input-date.component";
import * as i2 from "./input-date.directive";
import * as i3 from "./native-date/native-date.component";
import * as i4 from "@angular/common";
import * as i5 from "@maskito/angular";
import * as i6 from "@tinkoff/ng-polymorpheus";
import * as i7 from "@taiga-ui/core";
import * as i8 from "@taiga-ui/cdk";
import * as i9 from "@taiga-ui/kit/directives";
export declare class TuiInputDateModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputDateModule, [typeof i1.TuiInputDateComponent, typeof i2.TuiInputDateDirective, typeof i3.TuiNativeDateDirective], [typeof i4.CommonModule, typeof i5.MaskitoModule, typeof i6.PolymorpheusModule, typeof i7.TuiWrapperModule, typeof i8.TuiPreventDefaultModule, typeof i7.TuiCalendarModule, typeof i7.TuiSvgModule, typeof i7.TuiLinkModule, typeof i7.TuiHostedDropdownModule, typeof i7.TuiPrimitiveTextfieldModule, typeof i9.TuiValueAccessorModule, typeof i8.TuiLetModule, typeof i7.TuiTextfieldControllerModule], [typeof i1.TuiInputDateComponent, typeof i2.TuiInputDateDirective, typeof i7.TuiTextfieldComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputDateModule>;
}
