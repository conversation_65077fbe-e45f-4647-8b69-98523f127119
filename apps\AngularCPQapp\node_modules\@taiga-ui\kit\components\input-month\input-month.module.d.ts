import * as i0 from "@angular/core";
import * as i1 from "./input-month.component";
import * as i2 from "./input-month.directive";
import * as i3 from "@angular/common";
import * as i4 from "@taiga-ui/kit/components/calendar-month";
import * as i5 from "@taiga-ui/core";
import * as i6 from "@taiga-ui/cdk";
import * as i7 from "@angular/forms";
export declare class TuiInputMonthModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputMonthModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputMonthModule, [typeof i1.TuiInputMonthComponent, typeof i2.TuiInputMonthDirective], [typeof i3.CommonModule, typeof i4.TuiCalendarMonthModule, typeof i5.TuiHostedDropdownModule, typeof i5.TuiPrimitiveTextfieldModule, typeof i5.TuiSvgModule, typeof i6.TuiPreventDefaultModule, typeof i6.TuiMapperPipeModule, typeof i5.TuiTextfieldControllerModule, typeof i7.FormsModule], [typeof i1.TuiInputMonthComponent, typeof i2.TuiInputMonthDirective, typeof i5.TuiTextfieldComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputMonthModule>;
}
