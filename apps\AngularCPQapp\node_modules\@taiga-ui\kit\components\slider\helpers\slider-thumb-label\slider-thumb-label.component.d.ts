import { AfterContentInit } from '@angular/core';
import { NgControl } from '@angular/forms';
import { TuiSizeS } from '@taiga-ui/core';
import { TuiSliderComponent } from '../../slider.component';
import * as i0 from "@angular/core";
export declare class TuiSliderThumbLabelComponent implements AfterContentInit {
    readonly slider?: TuiSliderComponent;
    readonly control?: NgControl;
    get size(): TuiSizeS;
    get ratio(): number;
    get ghostLeft(): number;
    ngAfterContentInit(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSliderThumbLabelComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiSliderThumbLabelComponent, "[tuiSliderThumbLabel]", never, {}, {}, ["slider", "control"], ["*", "input[type=range]"]>;
}
