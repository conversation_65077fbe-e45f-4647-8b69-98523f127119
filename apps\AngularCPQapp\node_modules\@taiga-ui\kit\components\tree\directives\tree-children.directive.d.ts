import { TuiHandler } from '@taiga-ui/cdk';
import * as i0 from "@angular/core";
export declare class TuiTreeChildrenDirective<T> {
    childrenHandler: TuiHandler<T, readonly T[]>;
    static defaultHandler<G>(item: G): readonly G[];
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTreeChildrenDirective<any>, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiTreeChildrenDirective<any>, "tui-tree[childrenHandler]", never, { "childrenHandler": "childrenHandler"; }, {}, never>;
}
