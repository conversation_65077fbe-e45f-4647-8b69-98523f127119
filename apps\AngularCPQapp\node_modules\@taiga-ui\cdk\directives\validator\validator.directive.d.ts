import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { AbstractControl, ValidationErrors, Validator, ValidatorFn } from '@angular/forms';
import * as i0 from "@angular/core";
export declare class TuiValidatorDirective implements Validator, OnChanges, OnDestroy {
    private onChange;
    tuiValidator: ValidatorFn;
    validate(control: AbstractControl): ValidationErrors | null;
    registerOnValidatorChange(onChange: (...args: any[]) => void): void;
    ngOnChanges(): void;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiValidatorDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiValidatorDirective, "[tuiValidator]", never, { "tuiValidator": "tuiValidator"; }, {}, never>;
}
