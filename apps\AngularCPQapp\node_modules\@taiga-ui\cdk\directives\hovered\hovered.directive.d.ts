import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiHoveredDirective {
    readonly tuiHoveredChange: Observable<boolean>;
    constructor(tuiHoveredChange: Observable<boolean>);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiHoveredDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiHoveredDirective, "[tuiHoveredChange]", never, {}, { "tuiHoveredChange": "tuiHoveredChange"; }, never>;
}
