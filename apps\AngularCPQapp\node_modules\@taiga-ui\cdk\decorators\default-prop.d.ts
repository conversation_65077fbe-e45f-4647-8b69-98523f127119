import { TuiBoolean<PERSON>and<PERSON> } from '@taiga-ui/cdk/types';
/**
 * @deprecated:
 * not compatible with TypeScript 5
 *
 * Decorator for checking input values for undefined. You can also pass
 * optional assertion to check input against.
 *
 * CAUTION: This decorator overwrites other getters and setters.
 */
export declare function tuiDefaultProp<T extends Record<string, any>, K extends keyof T>(assertion?: TuiBooleanHandler<T[K]>, ...args: unknown[]): PropertyDecorator;
