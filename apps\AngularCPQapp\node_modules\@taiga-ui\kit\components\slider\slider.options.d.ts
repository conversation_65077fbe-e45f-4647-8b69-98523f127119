import { Provider } from '@angular/core';
import { TuiSizeS } from '@taiga-ui/core';
export interface TuiSliderOptions {
    readonly size: TuiSizeS;
    readonly trackColor: string;
}
export declare const TUI_SLIDER_DEFAULT_OPTIONS: TuiSliderOptions;
/**
 * Default parameters for Slider component
 */
export declare const TUI_SLIDER_OPTIONS: import("@angular/core").InjectionToken<TuiSliderOptions>;
export declare function tuiSliderOptionsProvider(options: Partial<TuiSliderOptions>): Provider;
