import * as i0 from "@angular/core";
import * as i1 from "./dialog-host.component";
import * as i2 from "@angular/common";
import * as i3 from "@tinkoff/ng-polymorpheus";
import * as i4 from "@taiga-ui/cdk/directives";
import * as i5 from "@taiga-ui/cdk/components/scroll-controls";
export declare class TuiDialogHostModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiDialogHostModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiDialogHostModule, [typeof i1.TuiDialogHostComponent], [typeof i2.CommonModule, typeof i3.PolymorpheusModule, typeof i4.TuiOverscrollModule, typeof i4.TuiFocusTrapModule, typeof i4.TuiLetModule, typeof i5.TuiScrollControlsModule], [typeof i1.TuiDialogHostComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiDialogHostModule>;
}
