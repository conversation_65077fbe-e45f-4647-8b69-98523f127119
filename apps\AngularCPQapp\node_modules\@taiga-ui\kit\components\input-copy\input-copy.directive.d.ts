import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { TuiInputCopyComponent } from './input-copy.component';
import * as i0 from "@angular/core";
export declare class TuiInputCopyDirective extends AbstractTuiTextfieldHost<TuiInputCopyComponent> {
    onValueChange(value: string): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputCopyDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputCopyDirective, "tui-input-copy", never, {}, {}, never>;
}
