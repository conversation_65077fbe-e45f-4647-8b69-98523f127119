import { ChangeDetectorRef, OnChanges, TemplateRef } from '@angular/core';
import { TuiDialog } from '@taiga-ui/cdk/types';
import { PolymorpheusTemplate } from '@tinkoff/ng-polymorpheus';
import { AbstractTuiDialogService } from './dialog.service';
import * as i0 from "@angular/core";
export declare abstract class AbstractTuiDialogDirective<T> extends PolymorpheusTemplate<TuiDialog<T, void>> implements OnChanges {
    private readonly service;
    private readonly open$;
    options: Partial<T>;
    open: boolean;
    openChange: import("rxjs").Observable<boolean>;
    constructor(templateRef: TemplateRef<TuiDialog<T, void>>, cdr: ChangeDetectorRef, service: AbstractTuiDialogService<T>);
    ngOnChanges(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<AbstractTuiDialogDirective<any>, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<AbstractTuiDialogDirective<any>, never, never, {}, {}, never>;
}
