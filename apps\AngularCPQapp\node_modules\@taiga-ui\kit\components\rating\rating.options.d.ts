import { Provider } from '@angular/core';
export interface TuiRatingOptions {
    readonly iconFilled: string;
    readonly iconNormal: string;
    readonly max: number;
    readonly min: number;
}
export declare const TUI_RATING_DEFAULT_OPTIONS: TuiRatingOptions;
/**
 * Default parameters for Rating component
 */
export declare const TUI_RATING_OPTIONS: import("@angular/core").InjectionToken<TuiRatingOptions>;
export declare function tuiRatingOptionsProvider(options: Partial<TuiRatingOptions>): Provider;
