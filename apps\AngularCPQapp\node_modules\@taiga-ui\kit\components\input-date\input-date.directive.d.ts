import { TuiDay } from '@taiga-ui/cdk';
import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { TuiInputDateComponent } from './input-date.component';
import * as i0 from "@angular/core";
export declare class TuiInputDateDirective extends AbstractTuiTextfieldHost<TuiInputDateComponent> {
    get value(): string;
    get max(): TuiDay;
    get min(): TuiDay;
    onValueChange(value: string): void;
    process(input: HTMLInputElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputDateDirective, "tui-input-date:not([multiple])", never, {}, {}, never>;
}
