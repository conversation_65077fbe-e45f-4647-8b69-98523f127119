import { TuiDateMode } from '@taiga-ui/cdk';
import { TuiCountryIsoCode } from '@taiga-ui/i18n';
import { Observable } from 'rxjs';
export declare const TUI_PROMPT_WORDS: import("@angular/core").InjectionToken<Observable<{
    no: string;
    yes: string;
}>>;
export declare const TUI_CANCEL_WORD: import("@angular/core").InjectionToken<Observable<string>>;
export declare const TUI_DONE_WORD: import("@angular/core").InjectionToken<Observable<string>>;
export declare const TUI_MORE_WORD: import("@angular/core").InjectionToken<Observable<string>>;
export declare const TUI_HIDE_TEXT: import("@angular/core").InjectionToken<Observable<string>>;
export declare const TUI_SHOW_ALL_TEXT: import("@angular/core").InjectionToken<Observable<string>>;
export declare const TUI_OTHER_DATE_TEXT: import("@angular/core").InjectionToken<Observable<string>>;
export declare const TUI_CHOOSE_DAY_OR_RANGE_TEXTS: import("@angular/core").InjectionToken<Observable<[string, string, string]>>;
export declare const TUI_FROM_TO_TEXTS: import("@angular/core").InjectionToken<Observable<[string, string]>>;
export declare const TUI_PLUS_MINUS_TEXTS: import("@angular/core").InjectionToken<Observable<[string, string]>>;
export declare const TUI_TIME_TEXTS: import("@angular/core").InjectionToken<Observable<{
    'HH:MM': string;
    'HH:MM:SS': string;
    'HH:MM:SS.MSS': string;
}>>;
export declare const TUI_DATE_TEXTS: import("@angular/core").InjectionToken<Observable<Record<TuiDateMode, string>>>;
export declare const TUI_DIGITAL_INFORMATION_UNITS: import("@angular/core").InjectionToken<Observable<[string, string, string]>>;
export declare const TUI_COPY_TEXTS: import("@angular/core").InjectionToken<Observable<[string, string]>>;
export declare const TUI_PASSWORD_TEXTS: import("@angular/core").InjectionToken<Observable<[string, string]>>;
export declare const TUI_CALENDAR_MONTHS: import("@angular/core").InjectionToken<Observable<[string, string, string, string, string, string, string, string, string, string, string, string]>>;
export declare const TUI_FILE_TEXTS: import("@angular/core").InjectionToken<Observable<{
    loadingError: string;
    preview: string;
    remove: string;
}>>;
export declare const TUI_PAGINATION_TEXTS: import("@angular/core").InjectionToken<Observable<[string, string]>>;
export declare const TUI_INPUT_FILE_TEXTS: import("@angular/core").InjectionToken<Observable<{
    defaultLabelMultiple: string;
    defaultLabelSingle: string;
    defaultLinkMultiple: string;
    defaultLinkSingle: string;
    drop: string;
    dropMultiple: string;
    formatRejectionReason: string;
    maxSizeRejectionReason: string;
}>>;
export declare const TUI_MULTI_SELECT_TEXTS: import("@angular/core").InjectionToken<Observable<{
    all: string;
    none: string;
}>>;
export declare const TUI_COUNTRIES: import("@angular/core").InjectionToken<Observable<Record<TuiCountryIsoCode, string>>>;
