import { Provider } from '@angular/core';
import { TuiContextWithImplicit, TuiDay } from '@taiga-ui/cdk';
import { TuiSizeL, TuiSizeS } from '@taiga-ui/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
export interface TuiInputDateOptions {
    readonly icon: PolymorpheusContent<TuiContextWithImplicit<TuiSizeL | TuiSizeS>>;
    readonly max: TuiDay;
    readonly min: TuiDay;
    readonly nativePicker: boolean;
}
export declare const TUI_INPUT_DATE_DEFAULT_OPTIONS: TuiInputDateOptions;
/**
 * Default parameters for InputDate component
 */
export declare const TUI_INPUT_DATE_OPTIONS: import("@angular/core").InjectionToken<TuiInputDateOptions>;
export declare function tuiInputDateOptionsProvider(options: Partial<TuiInputDateOptions>): Provider;
