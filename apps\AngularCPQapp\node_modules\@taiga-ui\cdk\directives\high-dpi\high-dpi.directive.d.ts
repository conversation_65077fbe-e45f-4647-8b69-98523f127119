import { TemplateRef, ViewContainerRef } from '@angular/core';
import * as i0 from "@angular/core";
/**
 * Only adds current content if user has High DPI display
 */
export declare class TuiHighDpiDirective {
    constructor({ devicePixelRatio }: Window, viewContainer: ViewContainerRef, templateRef: TemplateRef<Record<string, unknown>>);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiHighDpiDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiHighDpiDirective, "[tuiHighDpi]", never, {}, {}, never>;
}
