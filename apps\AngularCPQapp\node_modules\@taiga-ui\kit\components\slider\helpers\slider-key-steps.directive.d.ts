import { ChangeDetectorRef, ElementRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TuiFocusableElementAccessor } from '@taiga-ui/cdk';
import { TuiKeySteps } from '@taiga-ui/kit/types';
import { TuiSliderComponent } from '../slider.component';
import * as i0 from "@angular/core";
export declare class TuiSliderKeyStepsDirective extends AbstractTuiControl<number> implements TuiFocusableElementAccessor {
    private readonly el;
    private readonly slider;
    keySteps: TuiKeySteps;
    get nativeFocusableElement(): HTMLInputElement | null;
    get focused(): boolean;
    get min(): number;
    get max(): number;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, el: ElementRef<HTMLInputElement>, slider: TuiSliderComponent);
    updateControlValue(): void;
    writeValue(controlValue: number | null): void;
    protected getFallbackValue(): number;
    private transformToNativeValue;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSliderKeyStepsDirective, [{ optional: true; self: true; }, null, null, null]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiSliderKeyStepsDirective, "input[tuiSlider][keySteps]", never, { "keySteps": "keySteps"; }, {}, never>;
}
