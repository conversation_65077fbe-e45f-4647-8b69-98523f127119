import { fromEvent } from 'rxjs';
export function tuiTypedFromEvent(target, event, options = {}) {
    /**
     * @note:
     * in RxJS 7 type signature `TuiTypedEventTarget<E>` !== `HasEventTargetAddRemove<E>`
     */
    return fromEvent(target, event, options);
}
//# sourceMappingURL=data:application/json;base64,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