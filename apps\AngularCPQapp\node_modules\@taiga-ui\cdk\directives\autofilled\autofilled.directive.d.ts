import { EventEmitter } from '@angular/core';
import { TuiDirectiveStylesService } from '@taiga-ui/cdk/services';
import * as i0 from "@angular/core";
export declare class TuiAutofilledDirective {
    autofilled: boolean;
    readonly tuiAutofilledChange: EventEmitter<boolean>;
    constructor(directiveStyles: TuiDirectiveStylesService);
    transitionStartHandler({ propertyName, target }: TransitionEvent): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiAutofilledDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiAutofilledDirective, "[tuiAutofilledChange]", never, {}, { "tuiAutofilledChange": "tuiAutofilledChange"; }, never>;
}
