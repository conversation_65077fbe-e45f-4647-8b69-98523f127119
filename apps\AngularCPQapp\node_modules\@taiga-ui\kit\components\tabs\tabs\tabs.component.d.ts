import { AfterViewChecked, ChangeDetectorRef, ElementRef, QueryList } from '@angular/core';
import { Observable } from 'rxjs';
import { TuiTabsDirective } from '../tabs.directive';
import { TuiTabsOptions } from '../tabs.options';
import * as i0 from "@angular/core";
export declare class TuiTabsComponent implements AfterViewChecked {
    private readonly options;
    private readonly el;
    private readonly tabs;
    readonly children: QueryList<unknown>;
    underline: boolean;
    constructor(options: TuiTabsOptions, el: ElementRef<HTMLElement>, tabs: TuiTabsDirective, cdr: ChangeDetectorRef, resize$: Observable<void>);
    /** @deprecated use `activeItemIndex` from {@link TuiTabsDirective} instead */
    get activeItemIndex(): number;
    /** @deprecated use `activeItemIndex` from {@link TuiTabsDirective} instead */
    set activeItemIndex(index: number);
    get activeElement(): HTMLElement | null;
    onKeyDownArrow(current: HTMLElement, step: number): void;
    ngAfterViewChecked(): void;
    private scrollTo;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTabsComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiTabsComponent, "tui-tabs:not([vertical]), nav[tuiTabs]:not([vertical])", never, { "underline": "underline"; }, {}, ["children"], ["*"]>;
}
