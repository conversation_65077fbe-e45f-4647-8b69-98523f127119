import { ChangeDetectorRef, InjectionToken, QueryList } from '@angular/core';
import { NgControl } from '@angular/forms';
import { MaskitoOptions } from '@maskito/core';
import { AbstractTuiNullableControl, AbstractTuiValueTransformer, TuiFocusableElementAccessor, TuiInputMode } from '@taiga-ui/cdk';
import { TuiDecimal, TuiNumberFormatSettings, TuiSizeL, TuiSizeS, TuiTextfieldController, TuiTextfieldSizeDirective } from '@taiga-ui/core';
import { TuiInputNumberOptions } from './input-number.options';
import * as i0 from "@angular/core";
export declare const TUI_NUMBER_VALUE_TRANSFORMER: InjectionToken<AbstractTuiValueTransformer<number | null, unknown>>;
export declare class TuiInputNumberComponent extends AbstractTuiNullableControl<number> implements TuiFocusableElementAccessor {
    readonly options: TuiInputNumberOptions;
    private readonly numberFormat;
    private readonly isIOS;
    private readonly textfieldSize;
    readonly controller: TuiTextfieldController;
    private readonly textfield?;
    private unfinishedValue;
    min: number | null;
    max: number | null;
    decimal: TuiDecimal;
    precision: number;
    step: number;
    /** @deprecated use `tuiTextfieldPrefix` from {@link TuiTextfieldControllerModule} instead */
    prefix: string;
    /** @deprecated use `tuiTextfieldPostfix` from {@link TuiTextfieldControllerModule} instead */
    postfix: string;
    readonly polymorpheusValueContent: QueryList<unknown>;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, transformer: AbstractTuiValueTransformer<number | null>, options: TuiInputNumberOptions, numberFormat: TuiNumberFormatSettings, isIOS: boolean, textfieldSize: TuiTextfieldSizeDirective, controller: TuiTextfieldController);
    get size(): TuiSizeL | TuiSizeS;
    get computedMin(): number;
    get computedMax(): number;
    get nativeFocusableElement(): HTMLInputElement | null;
    get focused(): boolean;
    get isNegativeAllowed(): boolean;
    get inputMode(): TuiInputMode;
    get calculatedMaxLength(): number;
    get formattedValue(): string;
    get computedValue(): string;
    get canDecrement(): boolean;
    get canIncrement(): boolean;
    get computedPrefix(): string;
    get computedPostfix(): string;
    get mask(): MaskitoOptions;
    onArrow(step: number | null): void;
    onValueChange(nativeValue: string): void;
    onFocused(focused: boolean): void;
    getFormattedValue(value: number): string;
    private get isNativeValueNotFinished();
    get nativeValue(): string;
    set nativeValue(value: string);
    writeValue(value: number | null): void;
    private get nativeNumberValue();
    private computeMin;
    private computeMax;
    private calculateMask;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputNumberComponent, [{ optional: true; self: true; }, null, { optional: true; }, null, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputNumberComponent, "tui-input-number", never, { "min": "min"; "max": "max"; "decimal": "decimal"; "precision": "precision"; "step": "step"; "prefix": "prefix"; "postfix": "postfix"; }, {}, ["polymorpheusValueContent"], ["*", "input", "tuiContent"]>;
}
