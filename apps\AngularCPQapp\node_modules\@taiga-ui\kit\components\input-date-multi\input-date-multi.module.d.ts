import * as i0 from "@angular/core";
import * as i1 from "./input-date-multi.component";
import * as i2 from "@angular/common";
import * as i3 from "@maskito/angular";
import * as i4 from "@tinkoff/ng-polymorpheus";
import * as i5 from "@taiga-ui/core";
import * as i6 from "@taiga-ui/kit/components/input-tag";
import * as i7 from "@angular/forms";
import * as i8 from "@taiga-ui/cdk";
export declare class TuiInputDateMultiModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateMultiModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputDateMultiModule, [typeof i1.TuiInputDateMultiComponent], [typeof i2.CommonModule, typeof i3.MaskitoModule, typeof i4.PolymorpheusModule, typeof i5.TuiWrapperModule, typeof i5.TuiCalendarModule, typeof i5.TuiSvgModule, typeof i5.TuiLinkModule, typeof i6.TuiInputTagModule, typeof i7.FormsModule, typeof i8.TuiMapperPipeModule, typeof i5.TuiHostedDropdownModule, typeof i5.TuiTextfieldControllerModule, typeof i5.TuiPrimitiveTextfieldModule], [typeof i1.TuiInputDateMultiComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputDateMultiModule>;
}
