import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { TuiItemsHandlers } from '@taiga-ui/kit/tokens';
import { TuiSelectComponent } from './select.component';
import * as i0 from "@angular/core";
export declare class TuiSelectDirective extends AbstractTuiTextfieldHost<TuiSelectComponent<unknown>> {
    get readOnly(): boolean;
    get value(): string;
    get stringify(): TuiItemsHandlers<unknown>['stringify'];
    onValueChange(value: unknown): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSelectDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiSelectDirective, "tui-select", never, {}, {}, never>;
}
