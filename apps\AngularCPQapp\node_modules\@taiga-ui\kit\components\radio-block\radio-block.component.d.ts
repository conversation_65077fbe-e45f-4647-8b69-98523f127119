import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiNullableControl, TuiFocusableElementAccessor, TuiIdentityMatcher, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import { TuiAppearance, TuiHorizontalDirection, TuiModeDirective, TuiSizeL, TuiSizeXS } from '@taiga-ui/core';
import * as i0 from "@angular/core";
export declare class TuiRadioBlockComponent<T> extends AbstractTuiNullableControl<T> implements TuiFocusableElementAccessor {
    readonly modeDirective: TuiModeDirective | null;
    private readonly radio?;
    item?: T;
    identityMatcher: TuiIdentityMatcher<T>;
    contentAlign: TuiHorizontalDirection;
    size: TuiSizeL | TuiSizeXS;
    hideRadio: boolean;
    pseudoDisabled: boolean;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, modeDirective: TuiModeDirective | null);
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    get computedDisabled(): boolean;
    get checked(): boolean;
    get checkboxSize(): TuiSizeL;
    get appearance(): TuiAppearance;
    onFocused(focused: boolean): void;
    onFocusVisible(focusVisible: boolean): void;
    /** @deprecated use 'value' setter */
    onModelChange(value: T): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRadioBlockComponent<any>, [{ optional: true; self: true; }, null, { optional: true; }]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiRadioBlockComponent<any>, "tui-radio-block", never, { "item": "item"; "identityMatcher": "identityMatcher"; "contentAlign": "contentAlign"; "size": "size"; "hideRadio": "hideRadio"; "pseudoDisabled": "pseudoDisabled"; }, {}, never, ["*"]>;
}
