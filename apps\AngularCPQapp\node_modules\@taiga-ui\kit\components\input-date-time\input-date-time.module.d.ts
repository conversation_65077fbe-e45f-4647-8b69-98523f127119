import * as i0 from "@angular/core";
import * as i1 from "./input-date-time.component";
import * as i2 from "./input-date-time.directive";
import * as i3 from "./native-date-time/native-date-time.directive";
import * as i4 from "@angular/common";
import * as i5 from "@maskito/angular";
import * as i6 from "@tinkoff/ng-polymorpheus";
import * as i7 from "@taiga-ui/core";
import * as i8 from "@taiga-ui/cdk";
import * as i9 from "@taiga-ui/kit/directives";
export declare class TuiInputDateTimeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateTimeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputDateTimeModule, [typeof i1.TuiInputDateTimeComponent, typeof i2.TuiInputDateTimeDirective, typeof i3.TuiNativeDateTimeDirective], [typeof i4.CommonModule, typeof i5.MaskitoModule, typeof i6.PolymorpheusModule, typeof i7.TuiWrapperModule, typeof i8.TuiPreventDefaultModule, typeof i7.TuiCalendarModule, typeof i7.TuiSvgModule, typeof i7.TuiLinkModule, typeof i7.TuiHostedDropdownModule, typeof i7.TuiPrimitiveTextfieldModule, typeof i9.TuiValueAccessorModule, typeof i7.TuiTextfieldControllerModule], [typeof i1.TuiInputDateTimeComponent, typeof i2.TuiInputDateTimeDirective, typeof i7.TuiTextfieldComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputDateTimeModule>;
}
