import { ElementRef, Ng<PERSON>one } from '@angular/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiClickOutsideDirective {
    private readonly zone;
    private readonly doc;
    private readonly el;
    readonly tuiClickOutside: Observable<unknown>;
    constructor(zone: NgZone, doc: Document, el: ElementRef<HTMLElement>);
    private isOutside;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiClickOutsideDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiClickOutsideDirective, "[tuiClickOutside]", never, {}, { "tuiClickOutside": "tuiClickOutside"; }, never>;
}
