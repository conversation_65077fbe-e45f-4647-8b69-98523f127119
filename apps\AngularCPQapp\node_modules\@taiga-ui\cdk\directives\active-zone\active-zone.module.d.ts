import * as i0 from "@angular/core";
import * as i1 from "./active-zone.directive";
export declare class TuiActiveZoneModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiActiveZoneModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiActiveZoneModule, [typeof i1.TuiActiveZoneDirective], never, [typeof i1.TuiActiveZoneDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiActiveZoneModule>;
}
