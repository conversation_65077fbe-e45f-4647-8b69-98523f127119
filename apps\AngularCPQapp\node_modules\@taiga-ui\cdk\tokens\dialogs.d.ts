import { Provider, Type } from '@angular/core';
import { TuiAriaDialogContext } from '@taiga-ui/cdk/interfaces';
import { Observable } from 'rxjs';
/**
 * A stream of dialogs
 */
export declare const TUI_DIALOGS: import("@angular/core").InjectionToken<readonly Observable<readonly TuiAriaDialogContext[]>[]>;
export declare function tuiAsDialog(useExisting: Type<Observable<readonly TuiAriaDialogContext[]>>): Provider;
