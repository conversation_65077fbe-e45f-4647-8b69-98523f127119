import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiNullableControl, TuiFocusableElementAccessor, Tu<PERSON>H<PERSON><PERSON>, <PERSON><PERSON><PERSON>ont<PERSON>, <PERSON>iMonthRange, <PERSON>i<PERSON>ear } from '@taiga-ui/cdk';
import { TuiSizeL, TuiSizeS, TuiTextfieldSizeDirective, TuiWithOptionalMinMax } from '@taiga-ui/core';
import { TuiMonthContext } from '@taiga-ui/kit/interfaces';
import { TuiInputDateOptions } from '@taiga-ui/kit/tokens';
import { TuiBooleanHandlerWithContext } from '@taiga-ui/kit/types';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiInputMonthRangeComponent extends AbstractTuiNullableControl<TuiMonthRange> implements TuiWithOptionalMinMax<TuiMonth>, TuiFocusableElementAccessor {
    readonly formatter: <PERSON><PERSON><PERSON><PERSON><PERSON><TuiMonth | null, Observable<string>>;
    private readonly options;
    private readonly textfieldSize;
    private readonly textfield?;
    min: TuiMonth;
    max: TuiMonth;
    disabledItemHandler: TuiBooleanHandlerWithContext<TuiMonth, TuiMonthContext>;
    defaultActiveYear: TuiYear;
    open: boolean;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, formatter: TuiHandler<TuiMonth | null, Observable<string>>, options: TuiInputDateOptions, textfieldSize: TuiTextfieldSizeDirective);
    get size(): TuiSizeL | TuiSizeS;
    get nativeFocusableElement(): HTMLInputElement | null;
    get computedDefaultActiveYear(): TuiYear;
    get focused(): boolean;
    get calendarIcon(): TuiInputDateOptions['icon'];
    computeValue(from: string | null, to: string | null): string;
    onValueChange(value: string): void;
    onMonthClick(month: TuiMonth): void;
    onOpenChange(open: boolean): void;
    onActiveZone(focused: boolean): void;
    setDisabledState(): void;
    private close;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputMonthRangeComponent, [{ optional: true; self: true; }, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputMonthRangeComponent, "tui-input-month-range", never, { "min": "min"; "max": "max"; "disabledItemHandler": "disabledItemHandler"; "defaultActiveYear": "defaultActiveYear"; }, {}, never, ["*", "input"]>;
}
