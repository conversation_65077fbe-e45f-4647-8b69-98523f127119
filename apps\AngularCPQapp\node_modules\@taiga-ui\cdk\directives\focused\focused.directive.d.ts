import { ElementRef, Ng<PERSON>one } from '@angular/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
/**
 * Directive to monitor focus/blur status, works with focusIn/focus-out
 * instead of focus/blur to sync events order with Internet Explorer and
 * other focus related directives that require bubbling
 */
export declare class TuiFocusedDirective {
    readonly tuiFocusedChange: Observable<boolean>;
    constructor({ nativeElement }: ElementRef<HTMLElement>, zone: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiFocusedDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiFocusedDirective, "[tuiFocusedChange]", never, {}, { "tuiFocusedChange": "tuiFocusedChange"; }, never>;
}
