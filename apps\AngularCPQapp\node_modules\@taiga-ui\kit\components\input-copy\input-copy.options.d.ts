import { Provider } from '@angular/core';
import { TuiContextWithImplicit } from '@taiga-ui/cdk';
import { TuiHintDirection, TuiSizeL, TuiSizeS } from '@taiga-ui/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
export interface TuiInputCopyOptions {
    readonly icon: PolymorpheusContent<TuiContextWithImplicit<TuiSizeL | TuiSizeS>>;
    readonly messageAppearance: string;
    readonly messageDirection: TuiHintDirection;
    readonly successMessage: PolymorpheusContent;
}
export declare const TUI_INPUT_COPY_DEFAULT_OPTIONS: TuiInputCopyOptions;
export declare const TUI_INPUT_COPY_OPTIONS: import("@angular/core").InjectionToken<TuiInputCopyOptions>;
export declare function tuiInputCopyOptionsProvider(options: Partial<TuiInputCopyOptions>): Provider;
