import { PipeTransform } from '@angular/core';
import { TuiCountryIsoCode } from '@taiga-ui/i18n';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiSortCountriesPipe implements PipeTransform {
    private readonly countriesNames$;
    constructor(countriesNames$: Observable<Record<TuiCountryIsoCode, string>>);
    transform(countries: readonly TuiCountryIsoCode[]): Observable<TuiCountryIsoCode[]>;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSortCountriesPipe, never>;
    static ɵpipe: i0.ɵɵPipeDeclaration<TuiSortCountriesPipe, "tuiSortCountries">;
}
