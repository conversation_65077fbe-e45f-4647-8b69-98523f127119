import { DoCheck } from '@angular/core';
import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { TuiInputDateRangeComponent } from './input-date-range.component';
import * as i0 from "@angular/core";
export declare class TuiInputDateRangeDirective extends AbstractTuiTextfieldHost<TuiInputDateRangeComponent> implements DoCheck {
    get value(): string;
    onValueChange(value: string): void;
    process(input: HTMLInputElement): void;
    ngDoCheck(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateRangeDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputDateRangeDirective, "tui-input-date-range", never, {}, {}, never>;
}
