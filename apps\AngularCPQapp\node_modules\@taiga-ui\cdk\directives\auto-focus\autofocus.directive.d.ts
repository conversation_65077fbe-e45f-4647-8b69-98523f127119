import { AfterViewInit } from '@angular/core';
import { TuiDestroyService } from '@taiga-ui/cdk/services';
import { TuiAutofocusHandler, TuiAutofocusOptions } from './autofocus.options';
import * as i0 from "@angular/core";
export declare class TuiAutoFocusDirective implements AfterViewInit {
    private readonly handler;
    private readonly options;
    private readonly destroy$;
    autoFocus: boolean | '';
    constructor(handler: TuiA<PERSON>focusHand<PERSON>, options: TuiAutofocusOptions, destroy$: TuiDestroyService);
    ngAfterViewInit(): void;
    focus(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiAutoFocusDirective, [null, null, { self: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiAutoFocusDirective, "[tuiAutoFocus]", never, { "autoFocus": "tuiAutoFocus"; }, {}, never>;
}
