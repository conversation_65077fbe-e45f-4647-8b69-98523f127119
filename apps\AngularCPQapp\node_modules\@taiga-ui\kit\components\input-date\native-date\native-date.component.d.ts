import { TuiDateMode } from '@taiga-ui/cdk';
import type { TuiInputDateDirective } from '../input-date.directive';
import * as i0 from "@angular/core";
export declare class TuiNativeDateDirective {
    readonly host: TuiInputDateDirective;
    private readonly dateFormat;
    constructor(host: TuiInputDateDirective, dateFormat: TuiDateMode);
    get value(): string;
    get max(): string;
    get min(): string;
    onChange(value: string): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiNativeDateDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiNativeDateDirective, "input[tuiDate]", never, {}, {}, never>;
}
