import { inject } from '@angular/core';
import { tuiCreateTokenFromFactory } from '@taiga-ui/cdk/utils';
import { map } from 'rxjs/operators';
import { TUI_WINDOW_SIZE } from './window-size';
/**
 * @deprecated Use {@link TUI_WINDOW_SIZE} instead
 */
export const TUI_WINDOW_HEIGHT = tuiCreateTokenFromFactory(() => inject(TUI_WINDOW_SIZE).pipe(map(({ height }) => height)));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoid2luZG93LWhlaWdodC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uL3Byb2plY3RzL2Nkay90b2tlbnMvd2luZG93LWhlaWdodC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxPQUFPLEVBQUMsTUFBTSxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQ3JDLE9BQU8sRUFBQyx5QkFBeUIsRUFBQyxNQUFNLHFCQUFxQixDQUFDO0FBQzlELE9BQU8sRUFBQyxHQUFHLEVBQUMsTUFBTSxnQkFBZ0IsQ0FBQztBQUVuQyxPQUFPLEVBQUMsZUFBZSxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBRTlDOztHQUVHO0FBQ0gsTUFBTSxDQUFDLE1BQU0saUJBQWlCLEdBQUcseUJBQXlCLENBQUMsR0FBRyxFQUFFLENBQzVELE1BQU0sQ0FBQyxlQUFlLENBQUMsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLENBQUMsRUFBQyxNQUFNLEVBQUMsRUFBRSxFQUFFLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FDMUQsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7aW5qZWN0fSBmcm9tICdAYW5ndWxhci9jb3JlJztcbmltcG9ydCB7dHVpQ3JlYXRlVG9rZW5Gcm9tRmFjdG9yeX0gZnJvbSAnQHRhaWdhLXVpL2Nkay91dGlscyc7XG5pbXBvcnQge21hcH0gZnJvbSAncnhqcy9vcGVyYXRvcnMnO1xuXG5pbXBvcnQge1RVSV9XSU5ET1dfU0laRX0gZnJvbSAnLi93aW5kb3ctc2l6ZSc7XG5cbi8qKlxuICogQGRlcHJlY2F0ZWQgVXNlIHtAbGluayBUVUlfV0lORE9XX1NJWkV9IGluc3RlYWRcbiAqL1xuZXhwb3J0IGNvbnN0IFRVSV9XSU5ET1dfSEVJR0hUID0gdHVpQ3JlYXRlVG9rZW5Gcm9tRmFjdG9yeSgoKSA9PlxuICAgIGluamVjdChUVUlfV0lORE9XX1NJWkUpLnBpcGUobWFwKCh7aGVpZ2h0fSkgPT4gaGVpZ2h0KSksXG4pO1xuIl19