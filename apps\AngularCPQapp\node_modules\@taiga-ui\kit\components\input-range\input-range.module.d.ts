import * as i0 from "@angular/core";
import * as i1 from "./input-range.component";
import * as i2 from "@angular/common";
import * as i3 from "@angular/forms";
import * as i4 from "@tinkoff/ng-polymorpheus";
import * as i5 from "@taiga-ui/cdk";
import * as i6 from "@taiga-ui/kit/components/input-number";
import * as i7 from "@taiga-ui/kit/components/range";
import * as i8 from "@taiga-ui/core";
export declare class TuiInputRangeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputRangeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputRangeModule, [typeof i1.TuiInputRangeComponent], [typeof i2.CommonModule, typeof i3.FormsModule, typeof i4.PolymorpheusModule, typeof i5.TuiActiveZoneModule, typeof i6.TuiInputNumberModule, typeof i5.TuiPressedModule, typeof i7.TuiRangeModule, typeof i8.TuiWrapperModule, typeof i8.TuiTextfieldControllerModule], [typeof i1.TuiInputRangeComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputRangeModule>;
}
