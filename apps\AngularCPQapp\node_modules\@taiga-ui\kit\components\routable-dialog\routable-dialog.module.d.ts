import * as i0 from "@angular/core";
import * as i1 from "./routable-dialog.component";
export declare class TuiRoutableDialogModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRoutableDialogModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiRoutableDialogModule, [typeof i1.TuiRoutableDialogComponent], never, [typeof i1.TuiRoutableDialogComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiRoutableDialogModule>;
}
