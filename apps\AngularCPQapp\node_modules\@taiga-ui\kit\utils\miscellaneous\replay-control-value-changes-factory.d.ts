import { NgControl } from '@angular/forms';
import { TuiControlValueTransformer, TuiDay, TuiDayRange, TuiTime } from '@taiga-ui/cdk';
import { Observable } from 'rxjs';
/**
 * @internal
 */
export declare function tuiControlValueFactory<T extends TuiDay | TuiDayRange | [TuiDay | null, TuiTime | null]>(control: NgControl | null, valueTransformer?: TuiControlValueTransformer<T> | null): Observable<T | null> | null;
