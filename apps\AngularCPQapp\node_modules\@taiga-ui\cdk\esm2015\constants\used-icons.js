/**
 * @description:
 * AUTOGENERATED
 *
 * Array of icons used in taiga-ui components
 */
export const TUI_USED_ICONS = [
    'tuiIconMirMono',
    'tuiIconVisaMono',
    'tuiIconElectronMono',
    'tuiIconMastercard',
    'tuiIconMaestro',
    'tuiIconAmex',
    'tuiIconDinersClub',
    'tuiIconDiscover',
    'tuiIconHumo',
    'tuiIconJCB',
    'tuiIconRuPay',
    'tuiIconUnionPay',
    'tuiIconUzcard',
    'tuiIconVerve',
    'tuiIconCopyLarge',
    'tuiIconCheckLarge',
    'tuiIconLink',
    'tuiIconSearch',
    'tuiIconSun',
    'tuiIconMoon',
    'tuiIconCode',
    'tuiIconMenuLarge',
    'tuiIconRotate',
    'tuiIconArrowLeft',
    'tuiIconArrowRight',
    'tuiIconPlus',
    'tuiIconMinus',
    'tuiIconMinimize',
    'tuiIconEye',
    'tuiIconEyeOff',
    'tuiIconDrag',
    'tuiIconSortAscending',
    'tuiIconSortDescending',
    'tuiIconSortOff',
    'tuiIconCheck',
    'tuiIconMinusLarge',
    'tuiIconChevronUp',
    'tuiIconHelpCircle',
    'tuiIconClose',
    'tuiIconAlertCircle',
    'tuiIconChevronRight',
    'tuiIconInfo',
    'tuiIconCheckCircle',
    'tuiIconXCircle',
    'tuiIconChevronLeft',
    'tuiIconStarLarge',
    'tuiIconChevronDown',
    'tuiIconChevronDownLarge',
    'tuiIconFileLarge',
    'tuiIconCheckCircleLarge',
    'tuiIconAlertCircleLarge',
    'tuiIconTrashLarge',
    'tuiIconCopy',
    'tuiIconEyeOffLarge',
    'tuiIconEyeLarge',
    'tuiIconClock',
    'tuiIconClockLarge',
    'tuiIconToggleOff',
    'tuiIconToggleOffLarge',
    'tuiIconToggleOn',
    'tuiIconToggleOnLarge',
    'tuiIconCalendar',
    'tuiIconCalendarLarge',
];
//# sourceMappingURL=data:application/json;base64,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