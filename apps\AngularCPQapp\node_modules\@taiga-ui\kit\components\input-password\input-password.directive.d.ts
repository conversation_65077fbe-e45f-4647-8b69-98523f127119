import { DoCheck } from '@angular/core';
import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { TuiInputPasswordComponent } from './input-password.component';
import * as i0 from "@angular/core";
export declare class TuiInputPasswordDirective extends AbstractTuiTextfieldHost<TuiInputPasswordComponent> implements DoCheck {
    input?: HTMLInputElement;
    onValueChange(value: string): void;
    process(input: HTMLInputElement): void;
    ngDoCheck(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputPasswordDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputPasswordDirective, "tui-input-password", never, {}, {}, never>;
}
