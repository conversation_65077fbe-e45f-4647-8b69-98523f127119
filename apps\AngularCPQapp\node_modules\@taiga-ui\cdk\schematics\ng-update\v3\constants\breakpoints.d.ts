export declare const DEPRECATED_BREAKPOINTS: readonly [{
    readonly from: "@media-retina";
    readonly to: "@tui-media-retina";
}, {
    readonly from: "@media-retina-mobile";
    readonly to: "@tui-media-retina-mobile";
}, {
    readonly from: "@media-retina-tablet";
    readonly to: "@tui-media-retina-tablet";
}, {
    readonly from: "@media-retina-desktop";
    readonly to: "@tui-media-retina-desktop";
}, {
    readonly from: "@mobile-m";
    readonly to: "@tui-mobile";
}, {
    readonly from: "@mobile-m-min";
    readonly to: "@tui-mobile-min";
}, {
    readonly from: "@mobile-m-interval";
    readonly to: "@tui-mobile-interval";
}, {
    readonly from: "@tablet-lg";
    readonly to: "@tui-tablet";
}, {
    readonly from: "@tablet-lg-min";
    readonly to: "@tui-tablet-min";
}, {
    readonly from: "@tablet-lg-interval";
    readonly to: "@tui-tablet-interval";
}, {
    readonly from: "@desktop-s";
    readonly to: "@tui-desktop";
}, {
    readonly from: "@desktop-s-min";
    readonly to: "@tui-desktop-min";
}, {
    readonly from: "@desktop-s-interval";
    readonly to: "@tui-desktop-interval";
}, {
    readonly from: "@desktop-m-min";
    readonly to: "@tui-desktop-lg-min";
}, {
    readonly from: "@mobile";
    readonly to: "@tui-mobile";
}, {
    readonly from: "@mobile-min";
    readonly to: "@tui-mobile-min";
}, {
    readonly from: "@mobile-interval";
    readonly to: "@tui-mobile-interval";
}, {
    readonly from: "@tablet-s";
    readonly to: "@tui-mobile";
}, {
    readonly from: "@tablet-s-min";
    readonly to: "@tui-mobile-min";
}, {
    readonly from: "@tablet-s-interval";
    readonly to: "@tui-mobile-interval";
}, {
    readonly from: "@tablet";
    readonly to: "@tui-tablet";
}, {
    readonly from: "@tablet-min";
    readonly to: "@tui-tablet-min";
}, {
    readonly from: "@tablet-interval";
    readonly to: "@tui-tablet-interval";
}, {
    readonly from: "@desktop";
    readonly to: "@tui-desktop";
}, {
    readonly from: "@desktop-min";
    readonly to: "@tui-desktop-min";
}, {
    readonly from: "@desktop-interval";
    readonly to: "@tui-desktop-interval";
}, {
    readonly from: "@desktop-lg-min";
    readonly to: "@tui-desktop-lg-min";
}];
