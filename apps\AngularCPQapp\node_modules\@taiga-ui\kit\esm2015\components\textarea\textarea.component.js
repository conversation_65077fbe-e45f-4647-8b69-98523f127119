import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ContentChild, ElementRef, HostBinding, HostListener, Inject, Input, Optional, Self, ViewChild, } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TUI_IS_IOS, tuiAsControl, tuiAsFocusableItemAccessor, tuiIsNativeFocused, } from '@taiga-ui/cdk';
import { MODE_PROVIDER, TEXTFIELD_CONTROLLER_PROVIDER, TUI_ICON_PADDINGS, TUI_MODE, TUI_TEXTFIELD_WATCHED_CONTROLLER, tuiGetBorder, TuiHintOptionsDirective, TuiTextfieldComponent, } from '@taiga-ui/core';
import * as i0 from "@angular/core";
import * as i1 from "@taiga-ui/core";
import * as i2 from "@angular/common";
import * as i3 from "@angular/forms";
import * as i4 from "@taiga-ui/cdk";
import * as i5 from "@tinkoff/ng-polymorpheus";
import * as i6 from "rxjs";
export const DEFAULT_ROWS = 20;
export const LINE_HEIGHT_M = 20;
export const LINE_HEIGHT_L = 24;
export class TuiTextareaComponent extends AbstractTuiControl {
    constructor(control, cdr, isIOS, mode$, controller, hintOptions) {
        super(control, cdr);
        this.isIOS = isIOS;
        this.mode$ = mode$;
        this.controller = controller;
        this.hintOptions = hintOptions;
        this.rows = DEFAULT_ROWS;
        this.maxLength = null;
        this.expandable = false;
    }
    get labelOutside() {
        return this.controller.labelOutside;
    }
    get nativeFocusableElement() {
        var _a, _b;
        if (this.computedDisabled) {
            return null;
        }
        return (((_a = this.textfield) === null || _a === void 0 ? void 0 : _a.nativeElement) || ((_b = this.focusableElement) === null || _b === void 0 ? void 0 : _b.nativeElement) || null);
    }
    get focused() {
        return tuiIsNativeFocused(this.nativeFocusableElement);
    }
    get appearance() {
        return this.controller.appearance;
    }
    get size() {
        return this.controller.size;
    }
    get borderStart() {
        return this.iconLeftContent ? TUI_ICON_PADDINGS[this.size] : 0;
    }
    get borderEnd() {
        return tuiGetBorder(!!this.iconContent, this.hasCleaner, this.hasTooltip, this.hasCustomContent, this.size);
    }
    get hasCleaner() {
        return this.controller.cleaner && this.hasValue && this.interactive;
    }
    get hasTooltip() {
        var _a;
        return (!!((_a = this.hintOptions) === null || _a === void 0 ? void 0 : _a.content) &&
            (this.controller.options.hintOnDisabled || !this.computedDisabled));
    }
    get hasValue() {
        return this.value !== '';
    }
    get hasCounter() {
        return !!this.maxLength && this.interactive;
    }
    get hasPlaceholder() {
        return this.placeholderRaisable || (!this.hasValue && !this.hasExampleText);
    }
    get hasCustomContent() {
        return !!this.controller.customContent;
    }
    get iconLeftContent() {
        return this.controller.iconLeft;
    }
    get iconContent() {
        return this.controller.icon;
    }
    get iconCleaner() {
        return this.controller.options.iconCleaner;
    }
    get hasExampleText() {
        var _a;
        return (!!((_a = this.textfield) === null || _a === void 0 ? void 0 : _a.nativeElement.placeholder) &&
            this.focused &&
            !this.hasValue &&
            !this.readOnly);
    }
    get computeMaxHeight() {
        return this.expandable ? this.rows * this.lineHeight : null;
    }
    get placeholderRaised() {
        return (this.placeholderRaisable &&
            ((this.computedFocused && !this.readOnly) || this.hasValue));
    }
    get fittedContent() {
        return this.value.slice(0, this.maxLength || Infinity);
    }
    get extraContent() {
        return this.value.slice(this.maxLength || Infinity);
    }
    onFocused(focused) {
        this.updateFocused(focused);
    }
    onValueChange(value) {
        this.value = value;
    }
    onMouseDown(event) {
        if (event.target === this.nativeFocusableElement) {
            return;
        }
        event.preventDefault();
        if (this.nativeFocusableElement) {
            this.nativeFocusableElement.focus();
        }
    }
    getFallbackValue() {
        return '';
    }
    get lineHeight() {
        return this.controller.size === 'm' ? LINE_HEIGHT_M : LINE_HEIGHT_L;
    }
    get placeholderRaisable() {
        return this.size !== 's' && !this.controller.labelOutside;
    }
}
TuiTextareaComponent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiTextareaComponent, deps: [{ token: NgControl, optional: true, self: true }, { token: ChangeDetectorRef }, { token: TUI_IS_IOS }, { token: TUI_MODE }, { token: TUI_TEXTFIELD_WATCHED_CONTROLLER }, { token: TuiHintOptionsDirective, optional: true }], target: i0.ɵɵFactoryTarget.Component });
TuiTextareaComponent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: "12.0.0", version: "12.2.17", type: TuiTextareaComponent, selector: "tui-textarea", inputs: { rows: "rows", maxLength: "maxLength", expandable: "expandable" }, host: { listeners: { "$.data-mode.attr": "mode$", "focusin": "onFocused(true)", "focusout": "onFocused(false)" }, properties: { "class._ios": "isIOS", "class._expandable": "this.expandable", "class._label-outside": "this.labelOutside", "attr.data-size": "this.size", "style.--border-start.rem": "this.borderStart", "style.--border-end.rem": "this.borderEnd", "class._has-tooltip": "this.hasTooltip", "class._has-value": "this.hasValue", "class._has-counter": "this.hasCounter" } }, providers: [
        tuiAsFocusableItemAccessor(TuiTextareaComponent),
        tuiAsControl(TuiTextareaComponent),
        TEXTFIELD_CONTROLLER_PROVIDER,
        MODE_PROVIDER,
    ], queries: [{ propertyName: "textfield", first: true, predicate: TuiTextfieldComponent, descendants: true, read: ElementRef }], viewQueries: [{ propertyName: "focusableElement", first: true, predicate: ["focusableElement"], descendants: true }], usesInheritance: true, ngImport: i0, template: "<ng-container *ngIf=\"hintOptions?.change$ | async\"></ng-container>\n<div\n    automation-id=\"tui-text-area__wrapper\"\n    tuiWrapper\n    class=\"t-outline\"\n    [appearance]=\"appearance\"\n    [disabled]=\"disabled\"\n    [focus]=\"computedFocused\"\n    [hover]=\"pseudoHover\"\n    [invalid]=\"computedInvalid\"\n    [readOnly]=\"readOnly\"\n>\n    <div\n        *ngIf=\"hasCounter\"\n        automation-id=\"tui-text-area__counter\"\n        class=\"t-counter\"\n    >\n        {{ value.length }}/{{ maxLength }}\n    </div>\n\n    <label\n        class=\"t-content\"\n        (mousedown)=\"onMouseDown($event)\"\n    >\n        <div class=\"t-wrapper\">\n            <div\n                *ngIf=\"hasPlaceholder\"\n                automation-id=\"tui-text-area__placeholder\"\n                class=\"t-placeholder\"\n                [class.t-placeholder_raised]=\"placeholderRaised\"\n            >\n                <ng-content></ng-content>\n            </div>\n            <tui-scrollbar\n                automation-id=\"tui-text-area__scrollbar\"\n                class=\"t-box\"\n                [style.maxHeight.px]=\"computeMaxHeight\"\n            >\n                <div class=\"t-input-wrapper\">\n                    <div class=\"t-relative\">\n                        <div\n                            aria-hidden=\"true\"\n                            class=\"t-pseudo-content\"\n                        >\n                            <span [textContent]=\"fittedContent || nativeFocusableElement?.placeholder\"></span>\n                            <span\n                                class=\"t-pseudo-content__extra\"\n                                [textContent]=\"extraContent\"\n                            ></span>\n                            <span class=\"t-caret\"></span>\n                        </div>\n                        <textarea\n                            #focusableElement\n                            automation-id=\"tui-text-area__native\"\n                            class=\"t-input\"\n                            [disabled]=\"computedDisabled\"\n                            [id]=\"id\"\n                            [readOnly]=\"readOnly\"\n                            [tuiFocusable]=\"computedFocusable\"\n                            [(ngModel)]=\"value\"\n                        ></textarea>\n                        <ng-content select=\"textarea\"></ng-content>\n                    </div>\n                </div>\n            </tui-scrollbar>\n        </div>\n        <div class=\"t-icons\">\n            <div\n                *ngIf=\"iconLeftContent\"\n                class=\"t-icon t-icon_left t-textfield-icon\"\n            >\n                <tui-svg\n                    *polymorpheusOutlet=\"iconLeftContent as src; context: {$implicit: size}\"\n                    appearance=\"icon\"\n                    tuiWrapper\n                    [src]=\"src\"\n                ></tui-svg>\n            </div>\n            <ng-container *ngIf=\"hasCustomContent\">\n                <tui-svg\n                    *polymorpheusOutlet=\"controller.customContent as src\"\n                    [src]=\"src\"\n                ></tui-svg>\n            </ng-container>\n            <ng-container *ngIf=\"hasCleaner\">\n                <tui-svg\n                    *polymorpheusOutlet=\"iconCleaner as src; context: {$implicit: size}\"\n                    appearance=\"icon\"\n                    tuiWrapper\n                    class=\"t-cleaner\"\n                    [src]=\"src\"\n                    (click.stop)=\"onValueChange('')\"\n                ></tui-svg>\n            </ng-container>\n            <tui-tooltip\n                *ngIf=\"hasTooltip\"\n                automation-id=\"tui-text-area__tooltip\"\n                [content]=\"hintOptions?.content\"\n                [describeId]=\"id\"\n            ></tui-tooltip>\n            <div\n                *ngIf=\"iconContent\"\n                class=\"t-icon t-textfield-icon\"\n            >\n                <tui-svg\n                    *polymorpheusOutlet=\"iconContent as src; context: {$implicit: size}\"\n                    appearance=\"icon\"\n                    tuiWrapper\n                    [src]=\"src\"\n                ></tui-svg>\n            </div>\n        </div>\n    </label>\n</div>\n", styles: [":host{position:relative;z-index:0;display:flex;flex-direction:column;min-height:var(--tui-textarea-height);border-radius:var(--tui-radius-m);color:var(--tui-text-01)}:host[data-mode=onDark]{color:var(--tui-text-01-night)}:host[data-size=s]{--tui-height: var(--tui-height-s);--tui-textarea-height: 4.5625rem;font:var(--tui-font-text-s)}:host[data-size=m]{--tui-height: var(--tui-height-m);--tui-textarea-height: 5.5rem;font:var(--tui-font-text-s)}:host[data-size=l]{--tui-height: var(--tui-height-l);--tui-textarea-height: 6.75rem;font:var(--tui-font-text-m)}:host[data-size=m]._has-counter{--tui-textarea-height: 6.625rem}:host[data-size=l]._has-counter{--tui-textarea-height: 7.875rem}@supports (-webkit-hyphens: none){:host .t-pseudo-content,:host .t-input{text-wrap:balance;white-space:break-spaces}}.t-outline{min-height:inherit}.t-content{display:block;margin-top:0;margin-bottom:0;min-height:inherit;box-sizing:border-box;overflow:hidden;cursor:text}:host._disabled .t-content{cursor:auto;opacity:var(--tui-disabled-opacity)}:host:not(._expandable) .t-content{position:absolute;top:0;left:0;bottom:1px;right:0;min-height:auto}:host._has-counter:not(._expandable) .t-content{bottom:1.6875rem}:host._label-outside._has-counter:not(._expandable) .t-content{bottom:1rem}.t-wrapper{position:relative;width:100%;height:100%;min-height:inherit;box-sizing:border-box;padding:calc((var(--tui-height) - 1.25rem) / 2) 0}:host[data-size=l]._label-outside .t-wrapper{padding:calc((var(--tui-height) - 1.5rem) / 2) 0}:host[data-size=m]:not(._label-outside) .t-wrapper{padding:calc((var(--tui-height) - 2.25rem) / 2) 0}:host[data-size=l]:not(._label-outside) .t-wrapper{padding:calc((var(--tui-height) - 2.625rem) / 2) 0}:host-context(table)[data-size=m]._label-outside .t-wrapper{padding-bottom:.75rem}:host-context(table)[data-size=l]._label-outside .t-wrapper{padding-bottom:1rem}.t-input-wrapper{min-height:inherit;width:100%;flex:1}.t-relative{position:relative;min-height:inherit}.t-box{display:flex;min-height:calc(100% - 1rem);width:100%}:host:not(._expandable) .t-box{height:calc(100% - 1rem)}:host:not(._expandable)._label-outside .t-box{height:100%}:host._has-counter._expandable .t-box{margin-bottom:1.25rem}:host[data-size=m]:not(._label-outside) .t-box{border-top:1rem solid transparent}:host[data-size=l]:not(._label-outside) .t-box{border-top:1.25rem solid transparent}.t-pseudo-content{white-space:pre-wrap;word-wrap:break-word;pointer-events:none;color:transparent;overflow:hidden;border:0 solid transparent;border-inline-start-width:var(--border-start, 0);border-inline-end-width:var(--border-end, 0)}:host[data-size=s] .t-pseudo-content,:host[data-size=m] .t-pseudo-content{padding:0 .75rem}:host[data-size=l] .t-pseudo-content{padding:0 1rem}.t-pseudo-content__extra{background-color:var(--tui-error-bg-night)}.t-input{padding:0;margin:0;border-width:0;border-radius:inherit;background:none;font-size:inherit;line-height:inherit;font-weight:inherit;color:inherit;caret-color:currentColor;-webkit-appearance:none;-moz-appearance:none;appearance:none;word-break:keep-all;-webkit-text-fill-color:currentColor;position:absolute;top:0;left:0;width:100%;height:100%;box-sizing:border-box;resize:none;overflow:hidden;outline:none;border:0 solid transparent;border-inline-start-width:var(--border-start, 0);border-inline-end-width:var(--border-end, 0)}.t-input:-webkit-autofill,.t-input:-webkit-autofill:hover,.t-input:-webkit-autofill:focus{caret-color:var(--tui-base-09);border-radius:inherit;color:inherit!important;background-color:transparent!important;-webkit-text-fill-color:var(--tui-text-01)!important;border-color:var(--tui-autofill);-webkit-box-shadow:0 0 0 100rem var(--tui-autofill) inset!important}.t-input:not(:last-of-type){display:none}.t-input::placeholder{color:var(--tui-text-03);opacity:0}:host[data-mode=onDark] .t-input::placeholder{color:var(--tui-text-03-night)}:host._focused .t-input:not(:-moz-read-only)::placeholder{opacity:1}:host._focused .t-input:not(:read-only)::placeholder{opacity:1}:host[data-size=s] .t-input,:host[data-size=m] .t-input{padding:0 .75rem;font:var(--tui-font-text-s)}:host[data-size=l] .t-input{padding:0 1rem;font:var(--tui-font-text-m)}:host[data-mode=onDark]._disabled .t-input{color:var(--tui-text-03-night)}@supports (-webkit-marquee-repetition: infinite) and (object-fit: fill){:host._ios .t-input{padding-left:.8125rem}}.t-pseudo-content,.t-input{word-break:break-word}.t-placeholder{transition-property:transform,font-size,color,letter-spacing;transition-duration:var(--tui-duration, .3s);transition-timing-function:ease-in-out;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;width:100%;-webkit-user-select:none;-moz-user-select:none;user-select:none;font:var(--tui-font-text-s);color:var(--tui-text-02);pointer-events:none;will-change:transform;transform:translateY(0);position:absolute;top:calc(var(--tui-height) / 2 - .625rem);left:0;max-width:100%;border:0 solid transparent;border-inline-start-width:var(--border-start, 0);border-inline-end-width:var(--border-end, 0);box-sizing:border-box}@supports (-webkit-hyphens: none){.t-placeholder{will-change:unset;transition-property:transform,color,letter-spacing}}.t-placeholder_raised{transform:translateY(-.625rem)}:host[data-size=m] .t-placeholder_raised{font:var(--tui-font-text-xs);line-height:1.25rem;transform:translateY(-.5rem);letter-spacing:.025rem}:host._invalid:not(._focused) .t-placeholder_raised,:host._invalid:not(._focused):hover .t-placeholder_raised{color:var(--tui-error-fill)}:host._invalid:not(._focused) [tuiWrapper][data-mode=onDark] .t-placeholder_raised,:host._invalid:not(._focused):hover [tuiWrapper][data-mode=onDark] .t-placeholder_raised{color:var(--tui-error-fill-night)}:host._focused .t-placeholder,:host[data-size=m]._focused._label-outside .t-placeholder,:host[data-size=l]._focused._label-outside .t-placeholder{color:var(--tui-text-03)}:host[data-size=l] .t-placeholder{font:var(--tui-font-text-m);line-height:1.25rem}:host[data-size=l] .t-placeholder_raised{font-size:.8156rem}:host[data-size=m]._focused:not(._label-outside) .t-placeholder,:host[data-size=l]._focused:not(._label-outside) .t-placeholder{color:var(--tui-text-01)}[tuiWrapper][data-mode=onDark] .t-placeholder{color:var(--tui-text-02-night)}:host[data-size=m]._focused:not(._label-outside) [tuiWrapper][data-mode=onDark] .t-placeholder,:host[data-size=l]._focused:not(._label-outside) [tuiWrapper][data-mode=onDark] .t-placeholder{color:var(--tui-text-01-night)}:host._focused [tuiWrapper][data-mode=onDark] .t-placeholder,:host[data-size=m]._focused._label-outside [tuiWrapper][data-mode=onDark] .t-placeholder,:host[data-size=l]._focused._label-outside [tuiWrapper][data-mode=onDark] .t-placeholder{color:var(--tui-text-02-night)}:host[data-size=s] .t-placeholder{padding:0 .75rem}:host[data-size=m] .t-placeholder{padding:0 .75rem}:host[data-size=l] .t-placeholder{padding:0 1rem}:host._label-outside .t-placeholder{overflow:initial;height:auto;white-space:initial}.t-icons{position:absolute;top:0;left:0;bottom:0;right:0;display:flex;justify-content:flex-end;pointer-events:none;padding:calc((var(--tui-height) - 1.5rem) / 2) 1rem}:host[data-size=m] .t-icons{padding:calc((var(--tui-height) - 1.5rem) / 2) .625rem}.t-icons>:not(:first-child){-webkit-margin-start:.25rem;margin-inline-start:.25rem}.t-icon{position:relative;display:flex;width:1.5rem;height:1.5rem;align-items:center;justify-content:center;box-sizing:border-box;cursor:pointer;pointer-events:none}.t-icon_left{-webkit-margin-end:auto;margin-inline-end:auto}.t-cleaner{position:relative;display:flex;width:1.5rem;height:1.5rem;align-items:center;justify-content:center;box-sizing:border-box;cursor:pointer;pointer-events:none;pointer-events:auto}:host._readonly .t-cleaner,:host._disabled .t-cleaner{pointer-events:none}.t-caret{display:inline-block;height:1rem;width:0}.t-counter{position:absolute;right:.75rem;bottom:.5rem;font:var(--tui-font-text-s);pointer-events:none;margin-top:auto;text-align:right;color:var(--tui-text-03)}:host[data-mode=onDark] .t-counter{color:var(--tui-text-03-night)}\n"], components: [{ type: i1.TuiScrollbarComponent, selector: "tui-scrollbar", inputs: ["hidden"] }, { type: i1.TuiSvgComponent, selector: "tui-svg", inputs: ["src"] }, { type: i1.TuiTooltipComponent, selector: "tui-tooltip", inputs: ["content", "direction", "appearance", "showDelay", "hideDelay", "describeId", "context"] }], directives: [{ type: i2.NgIf, selector: "[ngIf]", inputs: ["ngIf", "ngIfThen", "ngIfElse"] }, { type: i1.TuiWrapperDirective, selector: "[tuiWrapper]", inputs: ["disabled", "readOnly", "hover", "active", "focus", "invalid", "appearance"] }, { type: i3.DefaultValueAccessor, selector: "input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]" }, { type: i4.TuiFocusableDirective, selector: "[tuiFocusable]", inputs: ["tuiFocusable"] }, { type: i3.NgControlStatus, selector: "[formControlName],[ngModel],[formControl]" }, { type: i3.NgModel, selector: "[ngModel]:not([formControlName]):not([formControl])", inputs: ["name", "disabled", "ngModel", "ngModelOptions"], outputs: ["ngModelChange"], exportAs: ["ngModel"] }, { type: i5.PolymorpheusOutletDirective, selector: "[polymorpheusOutlet]", inputs: ["polymorpheusOutlet", "polymorpheusOutletContext"] }], pipes: { "async": i2.AsyncPipe }, changeDetection: i0.ChangeDetectionStrategy.OnPush });
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiTextareaComponent, decorators: [{
            type: Component,
            args: [{
                    selector: 'tui-textarea',
                    templateUrl: './textarea.template.html',
                    styleUrls: ['./textarea.style.less'],
                    changeDetection: ChangeDetectionStrategy.OnPush,
                    providers: [
                        tuiAsFocusableItemAccessor(TuiTextareaComponent),
                        tuiAsControl(TuiTextareaComponent),
                        TEXTFIELD_CONTROLLER_PROVIDER,
                        MODE_PROVIDER,
                    ],
                    host: {
                        '($.data-mode.attr)': 'mode$',
                        '[class._ios]': 'isIOS',
                    },
                }]
        }], ctorParameters: function () { return [{ type: i3.NgControl, decorators: [{
                    type: Optional
                }, {
                    type: Self
                }, {
                    type: Inject,
                    args: [NgControl]
                }] }, { type: i0.ChangeDetectorRef, decorators: [{
                    type: Inject,
                    args: [ChangeDetectorRef]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TUI_IS_IOS]
                }] }, { type: i6.Observable, decorators: [{
                    type: Inject,
                    args: [TUI_MODE]
                }] }, { type: i1.TuiTextfieldController, decorators: [{
                    type: Inject,
                    args: [TUI_TEXTFIELD_WATCHED_CONTROLLER]
                }] }, { type: i1.TuiHintOptionsDirective, decorators: [{
                    type: Optional
                }, {
                    type: Inject,
                    args: [TuiHintOptionsDirective]
                }] }]; }, propDecorators: { focusableElement: [{
                type: ViewChild,
                args: ['focusableElement']
            }], textfield: [{
                type: ContentChild,
                args: [TuiTextfieldComponent, { read: ElementRef }]
            }], rows: [{
                type: Input
            }], maxLength: [{
                type: Input
            }], expandable: [{
                type: Input
            }, {
                type: HostBinding,
                args: ['class._expandable']
            }], labelOutside: [{
                type: HostBinding,
                args: ['class._label-outside']
            }], size: [{
                type: HostBinding,
                args: ['attr.data-size']
            }], borderStart: [{
                type: HostBinding,
                args: ['style.--border-start.rem']
            }], borderEnd: [{
                type: HostBinding,
                args: ['style.--border-end.rem']
            }], hasTooltip: [{
                type: HostBinding,
                args: ['class._has-tooltip']
            }], hasValue: [{
                type: HostBinding,
                args: ['class._has-value']
            }], hasCounter: [{
                type: HostBinding,
                args: ['class._has-counter']
            }], onFocused: [{
                type: HostListener,
                args: ['focusin', ['true']]
            }, {
                type: HostListener,
                args: ['focusout', ['false']]
            }] } });
//# sourceMappingURL=data:application/json;base64,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