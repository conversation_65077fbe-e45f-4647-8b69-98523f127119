import { <PERSON><PERSON><PERSON>roy } from '@angular/core';
import * as i0 from "@angular/core";
/**
 * Use this abstract class to create your own toggleable themes.
 * A component extending this class must have CSS variables definitions
 * and have ViewEncapsulation set to NONE. A boolean input allows to
 * switch theme on or off.
 */
export declare abstract class AbstractTuiThemeSwitcher implements OnDestroy {
    private readonly doc;
    static style: HTMLStyleElement | null;
    constructor(doc: Document);
    get style(): HTMLStyleElement | null;
    ngOnDestroy(): void;
    private addTheme;
    private removeTheme;
    static ɵfac: i0.ɵɵFactoryDeclaration<AbstractTuiThemeSwitcher, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<AbstractTuiThemeSwitcher, never, never, {}, {}, never>;
}
