import { TuiHorizontalDirection } from '@taiga-ui/core';
import { TuiTabsDirective } from '../tabs.directive';
import * as i0 from "@angular/core";
export declare class TuiTabsVerticalComponent {
    private readonly tabs;
    vertical: TuiHorizontalDirection;
    constructor(tabs: TuiTabsDirective);
    onKeyDownArrow(current: HTMLElement, step: number): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTabsVerticalComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiTabsVerticalComponent, "tui-tabs[vertical], nav[tuiTabs][vertical]", never, { "vertical": "vertical"; }, {}, never, ["*"]>;
}
