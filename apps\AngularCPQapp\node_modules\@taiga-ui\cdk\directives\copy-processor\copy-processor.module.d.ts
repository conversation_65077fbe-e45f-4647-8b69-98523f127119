import * as i0 from "@angular/core";
import * as i1 from "./copy-processor.directive";
export declare class TuiCopyProcessorModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiCopyProcessorModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiCopyProcessorModule, [typeof i1.TuiCopyProcessorDirective], never, [typeof i1.TuiCopyProcessorDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiCopyProcessorModule>;
}
