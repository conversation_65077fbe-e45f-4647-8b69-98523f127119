{"name": "@taiga-ui/kit", "version": "3.117.0", "description": "Taiga UI Angular main components kit", "keywords": ["angular", "kit", "component", "service", "directive"], "homepage": "https://github.com/taiga-family/taiga-ui", "repository": "https://github.com/taiga-family/taiga-ui", "license": "Apache-2.0", "dependencies": {"@maskito/angular": "^1.9.0", "@maskito/core": "^1.9.0", "@maskito/kit": "^1.9.0", "@ng-web-apis/intersection-observer": "^3.2.3", "text-mask-core": "^5.1.2", "tslib": ">=2.7.0"}, "peerDependencies": {"@angular/common": ">=12.0.0", "@angular/core": ">=12.0.0", "@angular/forms": ">=12.0.0", "@angular/router": ">=12.0.0", "@ng-web-apis/common": ">=3.2.3 <4", "@ng-web-apis/mutation-observer": ">=3.2.3 <4", "@ng-web-apis/resize-observer": ">=3.2.3 <4", "@taiga-ui/cdk": ">=3.117.0 <4", "@taiga-ui/core": ">=3.117.0 <4", "@taiga-ui/i18n": ">=3.117.0 <4", "@tinkoff/ng-polymorpheus": ">=4.3.0", "rxjs": ">=6.0.0"}, "main": "bundles/taiga-ui-kit.umd.js", "module": "fesm2015/taiga-ui-kit.js", "es2015": "fesm2015/taiga-ui-kit.js", "esm2015": "esm2015/taiga-ui-kit.js", "fesm2015": "fesm2015/taiga-ui-kit.js", "typings": "taiga-ui-kit.d.ts", "sideEffects": false}