import * as i0 from "@angular/core";
import * as i1 from "./lazy-loading.directive";
export declare class TuiLazyLoadingModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiLazyLoadingModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiLazyLoadingModule, [typeof i1.TuiLazyLoadingDirective], never, [typeof i1.TuiLazyLoadingDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiLazyLoadingModule>;
}
