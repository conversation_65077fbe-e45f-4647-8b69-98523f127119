import * as i0 from "@angular/core";
import * as i1 from "./tabs-with-more/tabs-with-more.component";
import * as i2 from "./tabs/tabs.component";
import * as i3 from "./tabs.directive";
import * as i4 from "./tabs-vertical/tabs-vertical.component";
import * as i5 from "./tab/tab.component";
import * as i6 from "./underline/underline.component";
import * as i7 from "@angular/common";
import * as i8 from "@tinkoff/ng-polymorpheus";
import * as i9 from "@taiga-ui/core";
import * as i10 from "@taiga-ui/cdk";
export declare class TuiTabsModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTabsModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiTabsModule, [typeof i1.TuiTabsWithMoreComponent, typeof i2.TuiTabsComponent, typeof i3.TuiTabsDirective, typeof i4.TuiTabsVerticalComponent, typeof i5.TuiTabComponent, typeof i6.TuiUnderlineComponent], [typeof i7.CommonModule, typeof i8.PolymorpheusModule, typeof i9.TuiHostedDropdownModule, typeof i9.TuiSvgModule, typeof i10.TuiFocusableModule, typeof i10.TuiItemModule], [typeof i1.TuiTabsWithMoreComponent, typeof i2.TuiTabsComponent, typeof i3.TuiTabsDirective, typeof i4.TuiTabsVerticalComponent, typeof i5.TuiTabComponent, typeof i10.TuiItemDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiTabsModule>;
}
