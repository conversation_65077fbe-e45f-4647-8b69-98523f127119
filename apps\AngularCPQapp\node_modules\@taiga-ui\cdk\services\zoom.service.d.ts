import { ElementRef } from '@angular/core';
import { Tui<PERSON>oom, TuiZoomOptions } from '@taiga-ui/cdk/interfaces';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiZoomService extends Observable<TuiZoom> {
    constructor({ nativeElement }: ElementRef<HTMLElement>, { wheelSensitivity }: TuiZoomOptions);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiZoomService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TuiZoomService>;
}
