{"main": "../../bundles/taiga-ui-cdk-directives-for-async.umd.js", "module": "../../fesm2015/taiga-ui-cdk-directives-for-async.js", "es2015": "../../fesm2015/taiga-ui-cdk-directives-for-async.js", "esm2015": "../../esm2015/directives/for-async/taiga-ui-cdk-directives-for-async.js", "fesm2015": "../../fesm2015/taiga-ui-cdk-directives-for-async.js", "typings": "taiga-ui-cdk-directives-for-async.d.ts", "sideEffects": false, "name": "@taiga-ui/cdk/directives/for-async"}