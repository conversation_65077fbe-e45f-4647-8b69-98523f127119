import { JsonArray } from '@angular-devkit/core';
import { Rule, SchematicContext, Tree } from '@angular-devkit/schematics';
import { TuiSchema } from '../ng-add/schema';
export declare function isInvalidAngularJson(tree: Tree): Promise<boolean>;
export declare function addStylesToAngularJson(options: TuiSchema, context: SchematicContext, taigaStyles: string[], filter?: (styles: JsonArray | undefined) => boolean, stylesToReplace?: {
    from: string;
    to: string[];
}, tree?: Tree): Rule;
