import { Provider, Type } from '@angular/core';
import { TuiAriaDialogContext } from '@taiga-ui/cdk/interfaces';
import { Observable } from 'rxjs';
/**
 * A stream of alerts
 */
export declare const TUI_ALERTS: import("@angular/core").InjectionToken<readonly Observable<readonly TuiAriaDialogContext[]>[]>;
export declare function tuiAsAlerts(useExisting: Type<Observable<readonly TuiAriaDialogContext[]>>): Provider;
