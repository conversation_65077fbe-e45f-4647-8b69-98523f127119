import * as i0 from "@angular/core";
import * as i1 from "./alert-host.component";
import * as i2 from "@angular/common";
import * as i3 from "@taiga-ui/cdk/pipes";
export declare class TuiAlertHostModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiAlertHostModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiAlertHostModule, [typeof i1.TuiAlertHostComponent], [typeof i2.CommonModule, typeof i3.TuiMapperPipeModule], [typeof i1.TuiAlertHostComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiAlertHostModule>;
}
