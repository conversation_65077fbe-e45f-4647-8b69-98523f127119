import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiNullableControl, TuiFocusableElementAccessor, TuiIdentityMatcher, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import { TuiBrightness, TuiSizeL } from '@taiga-ui/core';
import { TuiRadioOptions } from '@taiga-ui/kit/components/radio';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiRadioLabeledComponent<T> extends AbstractTuiNullableControl<T> implements TuiFocusableElementAccessor {
    readonly mode$: Observable<TuiBrightness | null>;
    private readonly options;
    private readonly radio?;
    item?: T;
    size: TuiSizeL;
    identityMatcher: TuiIdentityMatcher<T>;
    pseudoDisabled: boolean;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, mode$: Observable<TuiBrightness | null>, options: TuiRadioOptions);
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    get computedDisabled(): boolean;
    stopReadonlyChanging(event: Event): void;
    onFocused(focused: boolean): void;
    /** @deprecated use 'value' setter */
    onModelChange(value: T): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRadioLabeledComponent<any>, [{ optional: true; self: true; }, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiRadioLabeledComponent<any>, "tui-radio-labeled", never, { "item": "item"; "size": "size"; "identityMatcher": "identityMatcher"; "pseudoDisabled": "pseudoDisabled"; }, {}, never, ["*"]>;
}
