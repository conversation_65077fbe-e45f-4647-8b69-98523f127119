import { TuiZoom } from '@taiga-ui/cdk/interfaces';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiZoomDirective {
    readonly tuiZoom: Observable<TuiZoom>;
    constructor(tuiZoom: Observable<TuiZoom>);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiZoomDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiZoomDirective, "[tuiZoom]", never, {}, { "tuiZoom": "tuiZoom"; }, never>;
}
