import { On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, TemplateRef, ViewContainerRef } from '@angular/core';
import * as i0 from "@angular/core";
/**
 * @deprecated:
 * remove in v4.0
 */
export declare class TuiForAsyncDirective<T extends readonly any[]> implements OnChanges, OnD<PERSON>roy {
    private readonly view;
    private readonly template;
    private readonly destroy$;
    tuiForAsyncOf: T | null | undefined;
    tuiForAsyncTimeout: number;
    constructor(view: ViewContainerRef, template: TemplateRef<unknown>);
    ngOnChanges(): void;
    ngOnDestroy(): void;
    private createAsyncViewForNewNodes;
    private get iterableValues();
    private createEmbeddedView;
    private clearViewForOldNodes;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiForAsyncDirective<any>, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiForAsyncDirective<any>, "[tuiForAsync][tuiForAsyncOf]", never, { "tuiForAsyncOf": "tuiForAsyncOf"; "tuiForAsyncTimeout": "tuiForAsyncTimeout"; }, {}, never>;
}
