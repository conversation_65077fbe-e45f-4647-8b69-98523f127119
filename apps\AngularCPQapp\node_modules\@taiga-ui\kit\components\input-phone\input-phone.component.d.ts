import { ChangeDetectorRef, EventEmitter, TemplateRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { MaskitoOptions } from '@maskito/core';
import { AbstractTuiControl, TuiActiveZoneDirective, TuiContextWithImplicit, TuiFocusableElementAccessor, TuiInputMode } from '@taiga-ui/cdk';
import { TuiDataListHost, TuiSizeL, TuiSizeS, TuiTextfieldCleanerDirective, TuiTextfieldSizeDirective } from '@taiga-ui/core';
import { TuiInputPhoneOptions } from './input-phone.options';
import * as i0 from "@angular/core";
export declare class TuiInputPhoneComponent extends AbstractTuiControl<string> implements TuiFocusableElementAccessor, TuiDataListHost<string> {
    private readonly textfieldCleaner;
    private readonly options;
    private readonly textfieldSize;
    private readonly dropdown?;
    private readonly textfield?;
    set countryCodeSetter(newCountryCode: string);
    phoneMaskAfterCountryCode: string;
    allowText: boolean;
    search: string;
    readonly searchChange: EventEmitter<string>;
    readonly datalist?: TemplateRef<TuiContextWithImplicit<TuiActiveZoneDirective>>;
    countryCode: string;
    open: boolean;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, textfieldCleaner: TuiTextfieldCleanerDirective, options: TuiInputPhoneOptions, textfieldSize: TuiTextfieldSizeDirective);
    get size(): TuiSizeL | TuiSizeS;
    get nativeFocusableElement(): HTMLInputElement | null;
    get focused(): boolean;
    get nativeValue(): string;
    set nativeValue(value: string);
    get inputMode(): TuiInputMode;
    get canOpen(): boolean;
    get canClean(): boolean;
    get maskOptions(): MaskitoOptions;
    onActiveZone(active: boolean): void;
    onValueChange(value: string): void;
    handleOption(item: string): void;
    setDisabledState(): void;
    writeValue(value: string | null): void;
    protected getFallbackValue(): string;
    private get nonRemovablePrefix();
    private get maxPhoneLength();
    private get isTextValue();
    private calculateMask;
    private focusInput;
    private updateSearch;
    private updateValueWithNewCountryCode;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputPhoneComponent, [{ optional: true; self: true; }, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputPhoneComponent, "tui-input-phone", never, { "countryCodeSetter": "countryCode"; "phoneMaskAfterCountryCode": "phoneMaskAfterCountryCode"; "allowText": "allowText"; "search": "search"; }, { "searchChange": "searchChange"; }, ["datalist"], ["*", "input"]>;
}
