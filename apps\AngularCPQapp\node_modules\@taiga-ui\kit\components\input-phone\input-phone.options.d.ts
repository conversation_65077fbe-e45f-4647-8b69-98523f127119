import { Provider } from '@angular/core';
export interface TuiInputPhoneOptions {
    readonly allowText: boolean;
    readonly countryCode: string;
    readonly phoneMaskAfterCountryCode: string;
}
export declare const TUI_INPUT_PHONE_DEFAULT_OPTIONS: TuiInputPhoneOptions;
/**
 * Default parameters for input phone component
 */
export declare const TUI_INPUT_PHONE_OPTIONS: import("@angular/core").InjectionToken<TuiInputPhoneOptions>;
export declare function tuiInputPhoneOptionsProvider(options: Partial<TuiInputPhoneOptions>): Provider;
