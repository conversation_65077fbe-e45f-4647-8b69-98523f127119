import type { TuiInputDateTimeDirective } from '../input-date-time.directive';
import * as i0 from "@angular/core";
export declare class TuiNativeDateTimeDirective {
    readonly host: TuiInputDateTimeDirective;
    constructor(host: TuiInputDateTimeDirective);
    get value(): string;
    onChange(value: string): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiNativeDateTimeDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiNativeDateTimeDirective, "input[tuiDateTime]", never, {}, {}, never>;
}
