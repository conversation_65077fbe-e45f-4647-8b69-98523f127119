import * as i0 from "@angular/core";
import * as i1 from "./drag.directive";
/**
 * @deprecated not used anywhere
 */
export declare class TuiDragModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiDragModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiDragModule, [typeof i1.TuiDragDirective], never, [typeof i1.TuiDragDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiDragModule>;
}
