import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TuiFocusableModule } from '@taiga-ui/cdk';
import { TuiPrimitiveTextfieldModule, TuiScrollbarModule, TuiSvgModule, TuiTextfieldComponent, TuiTooltipModule, TuiWrapperModule, } from '@taiga-ui/core';
import { PolymorpheusModule } from '@tinkoff/ng-polymorpheus';
import { TuiTextareaComponent } from './textarea.component';
import { TuiTextareaDirective } from './textarea.directive';
import * as i0 from "@angular/core";
export class TuiTextareaModule {
}
TuiTextareaModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiTextareaModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });
TuiTextareaModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiTextareaModule, declarations: [TuiTextareaComponent, TuiTextareaDirective], imports: [CommonModule,
        FormsModule,
        TuiFocusableModule,
        TuiScrollbarModule,
        TuiTooltipModule,
        TuiWrapperModule,
        TuiSvgModule,
        TuiPrimitiveTextfieldModule,
        PolymorpheusModule], exports: [TuiTextareaComponent, TuiTextareaDirective, TuiTextfieldComponent] });
TuiTextareaModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiTextareaModule, imports: [[
            CommonModule,
            FormsModule,
            TuiFocusableModule,
            TuiScrollbarModule,
            TuiTooltipModule,
            TuiWrapperModule,
            TuiSvgModule,
            TuiPrimitiveTextfieldModule,
            PolymorpheusModule,
        ]] });
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "12.2.17", ngImport: i0, type: TuiTextareaModule, decorators: [{
            type: NgModule,
            args: [{
                    imports: [
                        CommonModule,
                        FormsModule,
                        TuiFocusableModule,
                        TuiScrollbarModule,
                        TuiTooltipModule,
                        TuiWrapperModule,
                        TuiSvgModule,
                        TuiPrimitiveTextfieldModule,
                        PolymorpheusModule,
                    ],
                    declarations: [TuiTextareaComponent, TuiTextareaDirective],
                    exports: [TuiTextareaComponent, TuiTextareaDirective, TuiTextfieldComponent],
                }]
        }] });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidGV4dGFyZWEubW9kdWxlLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vcHJvamVjdHMva2l0L2NvbXBvbmVudHMvdGV4dGFyZWEvdGV4dGFyZWEubW9kdWxlLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE9BQU8sRUFBQyxZQUFZLEVBQUMsTUFBTSxpQkFBaUIsQ0FBQztBQUM3QyxPQUFPLEVBQUMsUUFBUSxFQUFDLE1BQU0sZUFBZSxDQUFDO0FBQ3ZDLE9BQU8sRUFBQyxXQUFXLEVBQUMsTUFBTSxnQkFBZ0IsQ0FBQztBQUMzQyxPQUFPLEVBQUMsa0JBQWtCLEVBQUMsTUFBTSxlQUFlLENBQUM7QUFDakQsT0FBTyxFQUNILDJCQUEyQixFQUMzQixrQkFBa0IsRUFDbEIsWUFBWSxFQUNaLHFCQUFxQixFQUNyQixnQkFBZ0IsRUFDaEIsZ0JBQWdCLEdBQ25CLE1BQU0sZ0JBQWdCLENBQUM7QUFDeEIsT0FBTyxFQUFDLGtCQUFrQixFQUFDLE1BQU0sMEJBQTBCLENBQUM7QUFFNUQsT0FBTyxFQUFDLG9CQUFvQixFQUFDLE1BQU0sc0JBQXNCLENBQUM7QUFDMUQsT0FBTyxFQUFDLG9CQUFvQixFQUFDLE1BQU0sc0JBQXNCLENBQUM7O0FBaUIxRCxNQUFNLE9BQU8saUJBQWlCOzsrR0FBakIsaUJBQWlCO2dIQUFqQixpQkFBaUIsaUJBSFgsb0JBQW9CLEVBQUUsb0JBQW9CLGFBVnJELFlBQVk7UUFDWixXQUFXO1FBQ1gsa0JBQWtCO1FBQ2xCLGtCQUFrQjtRQUNsQixnQkFBZ0I7UUFDaEIsZ0JBQWdCO1FBQ2hCLFlBQVk7UUFDWiwyQkFBMkI7UUFDM0Isa0JBQWtCLGFBR1osb0JBQW9CLEVBQUUsb0JBQW9CLEVBQUUscUJBQXFCO2dIQUVsRSxpQkFBaUIsWUFkakI7WUFDTCxZQUFZO1lBQ1osV0FBVztZQUNYLGtCQUFrQjtZQUNsQixrQkFBa0I7WUFDbEIsZ0JBQWdCO1lBQ2hCLGdCQUFnQjtZQUNoQixZQUFZO1lBQ1osMkJBQTJCO1lBQzNCLGtCQUFrQjtTQUNyQjs0RkFJUSxpQkFBaUI7a0JBZjdCLFFBQVE7bUJBQUM7b0JBQ04sT0FBTyxFQUFFO3dCQUNMLFlBQVk7d0JBQ1osV0FBVzt3QkFDWCxrQkFBa0I7d0JBQ2xCLGtCQUFrQjt3QkFDbEIsZ0JBQWdCO3dCQUNoQixnQkFBZ0I7d0JBQ2hCLFlBQVk7d0JBQ1osMkJBQTJCO3dCQUMzQixrQkFBa0I7cUJBQ3JCO29CQUNELFlBQVksRUFBRSxDQUFDLG9CQUFvQixFQUFFLG9CQUFvQixDQUFDO29CQUMxRCxPQUFPLEVBQUUsQ0FBQyxvQkFBb0IsRUFBRSxvQkFBb0IsRUFBRSxxQkFBcUIsQ0FBQztpQkFDL0UiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge0NvbW1vbk1vZHVsZX0gZnJvbSAnQGFuZ3VsYXIvY29tbW9uJztcbmltcG9ydCB7TmdNb2R1bGV9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xuaW1wb3J0IHtGb3Jtc01vZHVsZX0gZnJvbSAnQGFuZ3VsYXIvZm9ybXMnO1xuaW1wb3J0IHtUdWlGb2N1c2FibGVNb2R1bGV9IGZyb20gJ0B0YWlnYS11aS9jZGsnO1xuaW1wb3J0IHtcbiAgICBUdWlQcmltaXRpdmVUZXh0ZmllbGRNb2R1bGUsXG4gICAgVHVpU2Nyb2xsYmFyTW9kdWxlLFxuICAgIFR1aVN2Z01vZHVsZSxcbiAgICBUdWlUZXh0ZmllbGRDb21wb25lbnQsXG4gICAgVHVpVG9vbHRpcE1vZHVsZSxcbiAgICBUdWlXcmFwcGVyTW9kdWxlLFxufSBmcm9tICdAdGFpZ2EtdWkvY29yZSc7XG5pbXBvcnQge1BvbHltb3JwaGV1c01vZHVsZX0gZnJvbSAnQHRpbmtvZmYvbmctcG9seW1vcnBoZXVzJztcblxuaW1wb3J0IHtUdWlUZXh0YXJlYUNvbXBvbmVudH0gZnJvbSAnLi90ZXh0YXJlYS5jb21wb25lbnQnO1xuaW1wb3J0IHtUdWlUZXh0YXJlYURpcmVjdGl2ZX0gZnJvbSAnLi90ZXh0YXJlYS5kaXJlY3RpdmUnO1xuXG5ATmdNb2R1bGUoe1xuICAgIGltcG9ydHM6IFtcbiAgICAgICAgQ29tbW9uTW9kdWxlLFxuICAgICAgICBGb3Jtc01vZHVsZSxcbiAgICAgICAgVHVpRm9jdXNhYmxlTW9kdWxlLFxuICAgICAgICBUdWlTY3JvbGxiYXJNb2R1bGUsXG4gICAgICAgIFR1aVRvb2x0aXBNb2R1bGUsXG4gICAgICAgIFR1aVdyYXBwZXJNb2R1bGUsXG4gICAgICAgIFR1aVN2Z01vZHVsZSxcbiAgICAgICAgVHVpUHJpbWl0aXZlVGV4dGZpZWxkTW9kdWxlLFxuICAgICAgICBQb2x5bW9ycGhldXNNb2R1bGUsXG4gICAgXSxcbiAgICBkZWNsYXJhdGlvbnM6IFtUdWlUZXh0YXJlYUNvbXBvbmVudCwgVHVpVGV4dGFyZWFEaXJlY3RpdmVdLFxuICAgIGV4cG9ydHM6IFtUdWlUZXh0YXJlYUNvbXBvbmVudCwgVHVpVGV4dGFyZWFEaXJlY3RpdmUsIFR1aVRleHRmaWVsZENvbXBvbmVudF0sXG59KVxuZXhwb3J0IGNsYXNzIFR1aVRleHRhcmVhTW9kdWxlIHt9XG4iXX0=