import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiNullableControl, TuiActiveZoneDirective, TuiContextWithImplicit, TuiFocusableElementAccessor } from '@taiga-ui/cdk';
import { TuiDataListHost, TuiSizeL, TuiSizeM, TuiSizeS, TuiTextfieldCleanerDirective, TuiTextfieldSizeDirective, TuiValueContentContext } from '@taiga-ui/core';
import { TuiArrowMode } from '@taiga-ui/kit/components/arrow';
import { TuiItemsHandlers } from '@taiga-ui/kit/tokens';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { TuiSelectOptions } from './select.options';
import * as i0 from "@angular/core";
export declare class TuiSelectComponent<T> extends AbstractTuiNullableControl<T> implements TuiFocusableElementAccessor, TuiDataListHost<T> {
    private readonly textfieldCleaner;
    private readonly textfieldSize;
    private readonly arrowMode;
    private readonly itemsHandlers;
    private readonly options;
    readonly isMobile: boolean;
    private readonly textfield?;
    private readonly hostedDropdown?;
    private readonly nativeSelect?;
    stringify: TuiItemsHandlers<T>['stringify'];
    identityMatcher: TuiItemsHandlers<T>['identityMatcher'];
    valueContent: TuiSelectOptions<T>['valueContent'];
    readonly datalist: PolymorpheusContent<TuiContextWithImplicit<TuiActiveZoneDirective>>;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, textfieldCleaner: TuiTextfieldCleanerDirective, textfieldSize: TuiTextfieldSizeDirective, arrowMode: TuiArrowMode, itemsHandlers: TuiItemsHandlers<T>, options: TuiSelectOptions<T>, isMobile: boolean);
    get size(): TuiSizeL | TuiSizeS;
    get arrow(): PolymorpheusContent<TuiContextWithImplicit<TuiSizeL | TuiSizeM | TuiSizeS>>;
    get nativeFocusableElement(): HTMLInputElement | null;
    get focused(): boolean;
    get nativeDropdownMode(): boolean;
    get computedValue(): string;
    get computedContent(): PolymorpheusContent<TuiValueContentContext<T>>;
    onValueChange(value: T): void;
    onActiveZone(active: boolean): void;
    onKeyDownDelete(): void;
    handleOption(option: T): void;
    private focusInput;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiSelectComponent<any>, [{ optional: true; self: true; }, null, null, null, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiSelectComponent<any>, "tui-select", never, { "stringify": "stringify"; "identityMatcher": "identityMatcher"; "valueContent": "valueContent"; }, {}, ["nativeSelect", "datalist"], ["*", "input", "select"]>;
}
