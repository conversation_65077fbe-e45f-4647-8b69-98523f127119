import { Do<PERSON>heck } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@taiga-ui/cdk';
import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { Observable } from 'rxjs';
import { TuiInputMonthComponent } from './input-month.component';
import * as i0 from "@angular/core";
export declare class TuiInputMonthDirective extends AbstractTuiTextfieldHost<TuiInputMonthComponent> implements DoCheck {
    private readonly value$;
    private localizedValue;
    constructor(host: TuiInputMonthComponent, formatter: TuiHandler<TuiMonth | null, Observable<string>>, destroy$: Observable<unknown>);
    get readOnly(): boolean;
    get value(): string;
    ngDoCheck(): void;
    onValueChange(value: string): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputMonthDirective, [null, null, { self: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputMonthDirective, "tui-input-month", never, {}, {}, never>;
}
