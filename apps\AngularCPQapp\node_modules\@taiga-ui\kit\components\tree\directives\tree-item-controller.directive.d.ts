import { TuiTreeItemComponent } from '../components/tree-item/tree-item.component';
import { TuiTreeController } from '../misc/tree.interfaces';
import * as i0 from "@angular/core";
export declare class TuiTreeItemControllerDirective implements TuiTreeController {
    private readonly map;
    fallback: boolean;
    isExpanded(item: TuiTreeItemComponent): boolean;
    toggle(item: TuiTreeItemComponent): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTreeItemControllerDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiTreeItemControllerDirective, "[tuiTreeController]:not([map])", ["tuiTreeController"], { "fallback": "tuiTreeController"; }, {}, never>;
}
