import { TuiStringHandler } from '@taiga-ui/cdk/types';
import * as i0 from "@angular/core";
export declare class TuiCopyProcessorDirective {
    private readonly win;
    tuiCopyProcessor: TuiStringHandler<string>;
    constructor(win: Window);
    onCopy(event: ClipboardEvent): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiCopyProcessorDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiCopyProcessorDirective, "[tuiCopyProcessor]", never, { "tuiCopyProcessor": "tuiCopyProcessor"; }, {}, never>;
}
