import * as i0 from "@angular/core";
import * as i1 from "./input-phone.component";
import * as i2 from "./input-phone.directive";
import * as i3 from "@maskito/angular";
import * as i4 from "@taiga-ui/core";
import * as i5 from "@taiga-ui/cdk";
import * as i6 from "@taiga-ui/kit/directives";
export declare class TuiInputPhoneModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputPhoneModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputPhoneModule, [typeof i1.TuiInputPhoneComponent, typeof i2.TuiInputPhoneDirective], [typeof i3.MaskitoModule, typeof i4.TuiPrimitiveTextfieldModule, typeof i4.TuiHostedDropdownModule, typeof i4.TuiTextfieldControllerModule, typeof i5.TuiActiveZoneModule, typeof i6.TuiValueAccessorModule], [typeof i1.TuiInputPhoneComponent, typeof i2.TuiInputPhoneDirective, typeof i4.TuiTextfieldComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputPhoneModule>;
}
