import { ReplacementAttributeToDirective } from '../../interfaces';
import { RemovableInput } from '../../interfaces/removable-input';
import { ReplacementAttribute } from '../../interfaces/replacement-attribute';
import { ReplacementAttributeValue } from '../../interfaces/replacement-attribute-value';
import { ReplacementTag } from '../../interfaces/replacement-tag';
export declare const ATTRS_TO_REPLACE: ReplacementAttribute[];
export declare const INPUTS_TO_REMOVE: RemovableInput[];
export declare const TAGS_TO_REPLACE: ReplacementTag[];
export declare const ATTR_TO_DIRECTIVE: ReplacementAttributeToDirective[];
/**
 * @example `<div [someDirective]="true" />` => `<div someDirective />`
 *
 * Keeping it for future sake
 */
export declare const TRUTHY_BOOLEAN_INPUT_TO_HTML_BINARY_ATTRIBUTE: readonly string[];
export declare const TEMPLATE_COMMENTS: readonly [{
    readonly tag: "tui-input-slider";
    readonly withAttr: "pluralize";
    readonly comment: "[pluralize] => Use [postfix] instead. See https://taiga-ui.dev/components/input-slider/API?postfix=apples";
}, {
    readonly tag: "tui-input-slider";
    readonly withAttr: "segmentsPluralize";
    readonly comment: "See examples how create labels for ticks without this property (outside the component): https://taiga-ui.dev/components/input-slider#slider-segments";
}, {
    readonly tag: "tui-input-range";
    readonly withAttr: "segmentsPluralize";
    readonly comment: "See examples how create labels for ticks without this property (outside the component): https://taiga-ui.dev/components/input-range#segments";
}, {
    readonly tag: "tui-range";
    readonly withAttr: "pluralize";
    readonly comment: "See examples how create labels for ticks without this property (outside the component): https://taiga-ui.dev/components/range#segments";
}, {
    readonly tag: "tui-range";
    readonly withAttr: "steps";
    readonly comment: "This component has new API. Use property [step] instead. See: https://taiga-ui.dev/components/range/API";
}, {
    readonly tag: "tui-preview-host";
    readonly withAttr: "ngProjectAs";
    readonly comment: "\"Preview\"-component no longer needs it and requires only import of TuiPreviewModule to the main module. See \"Setup\"-section: https://taiga-ui.dev/components/preview/Setup";
}, {
    readonly tag: "tui-progress";
    readonly withAttr: "value";
    readonly comment: "This legacy component was replaced by 3 new ones (https://taiga-ui.dev/components/progress-bar | https://taiga-ui.dev/components/progress-circle | https://taiga-ui.dev/components/progress-segmented ) ";
}, {
    readonly tag: "tui-input-file";
    readonly withAttr: "loadingFiles";
    readonly comment: "This legacy component was replaced by new one (https://taiga-ui.dev/components/input-files) ";
}, {
    readonly tag: "tui-input-tag";
    readonly withAttr: "allowSpaces";
    readonly comment: "Use property [separator] to forbid spaces. See example: https://taiga-ui.dev/components/input-tag#no-spaces-inside-tags";
}, {
    readonly tag: "tui-preview-pagination";
    readonly withAttr: "lastIndex";
    readonly comment: "Use property [length] instead. See example: https://taiga-ui.dev/components/preview";
}];
export declare const REPLACE_ATTR_VALUE: ReplacementAttributeValue[];
