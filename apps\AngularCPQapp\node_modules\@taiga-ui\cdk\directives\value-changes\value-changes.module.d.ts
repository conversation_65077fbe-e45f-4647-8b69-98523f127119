import * as i0 from "@angular/core";
import * as i1 from "./value-changes.directive";
export declare class TuiValueChangesModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiValueChangesModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiValueChangesModule, [typeof i1.TuiValueChangesDirective], never, [typeof i1.TuiValueChangesDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiValueChangesModule>;
}
