import * as i0 from "@angular/core";
import * as i1 from "./stepper.component";
import * as i2 from "./step/step.component";
import * as i3 from "@angular/common";
import * as i4 from "@taiga-ui/core";
export declare class TuiStepperModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiStepperModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiStepperModule, [typeof i1.TuiStepperComponent, typeof i2.TuiStepComponent], [typeof i3.CommonModule, typeof i4.TuiSvgModule], [typeof i1.TuiStepperComponent, typeof i2.TuiStepComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiStepperModule>;
}
