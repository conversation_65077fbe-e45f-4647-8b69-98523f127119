import { ElementRef } from '@angular/core';
import { TuiIdService } from '@taiga-ui/cdk';
import { TuiInputFilesComponent } from './input-files.component';
import { TuiInputFilesOptions } from './input-files.options';
import * as i0 from "@angular/core";
export declare class TuiInputFilesDirective {
    readonly host: TuiInputFilesComponent;
    private readonly el;
    private readonly idService;
    private readonly options;
    constructor(host: TuiInputFilesComponent, el: ElementRef<HTMLInputElement>, idService: TuiIdService, options: TuiInputFilesOptions);
    get tabIndex(): number;
    get id(): string;
    get accept(): string;
    get multiple(): boolean;
    get capture(): string | null;
    get input(): HTMLInputElement;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputFilesDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputFilesDirective, "input[tuiInputFiles]", never, {}, {}, never>;
}
