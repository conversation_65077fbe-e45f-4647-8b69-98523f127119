import { ChangeDetector<PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit, Provider, Type } from '@angular/core';
import { AbstractControl, ControlValueAccessor, NgControl } from '@angular/forms';
import { TuiControlValueTransformer } from '@taiga-ui/cdk/interfaces';
import { Subject } from 'rxjs';
import { AbstractTuiInteractive } from './interactive';
import * as i0 from "@angular/core";
/**
 * Basic ControlValueAccessor class to build form components upon
 */
export declare abstract class AbstractTuiControl<T> extends AbstractTuiInteractive implements OnD<PERSON>roy, OnInit, ControlValueAccessor {
    private readonly ngControl;
    protected readonly cdr: ChangeDetectorRef;
    protected readonly valueTransformer?: TuiControlValueTransformer<T, unknown> | null | undefined;
    private previousInternalValue?;
    private readonly refresh$;
    private onTouched;
    private onChange;
    protected readonly fallbackValue: T;
    protected readonly destroy$: Subject<void>;
    readOnly: boolean;
    pseudoInvalid: boolean | null;
    constructor(ngControl: NgControl | null, cdr: ChangeDetectorRef, valueTransformer?: TuiControlValueTransformer<T, unknown> | null | undefined);
    protected abstract getFallbackValue(): T;
    get computedInvalid(): boolean;
    get value(): T;
    set value(value: T);
    get safeCurrentValue(): T;
    get invalid(): boolean;
    get valid(): boolean;
    get touched(): boolean;
    get disabled(): boolean;
    get interactive(): boolean;
    get control(): AbstractControl | null;
    get computedName(): string | null;
    protected get controlName(): string | null;
    private get rawValue();
    ngOnInit(): void;
    ngOnDestroy(): void;
    checkControlUpdate(): void;
    registerOnChange(onChange: (value: T | unknown) => void): void;
    registerOnTouched(onTouched: () => void): void;
    setDisabledState(): void;
    writeValue(value: T | null): void;
    protected updateFocused(focused: boolean): void;
    /**
     * @deprecated use `value` setter
     */
    protected updateValue(value: T): void;
    protected valueIdenticalComparator(oldValue: T, newValue: T): boolean;
    private safeNgControlData;
    private controlMarkAsTouched;
    private controlSetValue;
    private refreshLocalValue;
    private fromControlValue;
    private toControlValue;
    static ɵfac: i0.ɵɵFactoryDeclaration<AbstractTuiControl<any>, [{ optional: true; }, null, { optional: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<AbstractTuiControl<any>, never, never, { "readOnly": "readOnly"; "pseudoInvalid": "pseudoInvalid"; }, {}, never>;
}
export declare function tuiAsControl<T>(useExisting: Type<AbstractTuiControl<T>>): Provider;
