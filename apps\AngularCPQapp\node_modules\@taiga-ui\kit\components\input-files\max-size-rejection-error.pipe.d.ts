import { PipeTransform } from '@angular/core';
import { TuiInjectionTokenType } from '@taiga-ui/cdk';
import { TUI_DIGITAL_INFORMATION_UNITS, TUI_INPUT_FILE_TEXTS } from '@taiga-ui/kit/tokens';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiMaxSizeRejectionErrorPipe implements PipeTransform {
    private readonly inputFileTexts$;
    private readonly units$;
    constructor(inputFileTexts$: TuiInjectionTokenType<typeof TUI_INPUT_FILE_TEXTS>, units$: TuiInjectionTokenType<typeof TUI_DIGITAL_INFORMATION_UNITS>);
    transform(maxFileSize: number): Observable<string>;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiMaxSizeRejectionErrorPipe, never>;
    static ɵpipe: i0.ɵɵPipeDeclaration<TuiMaxSizeRejectionErrorPipe, "tuiMaxSizeRejectionError">;
}
