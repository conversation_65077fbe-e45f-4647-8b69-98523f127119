import { UpdateRecorder } from '@angular-devkit/schematics';
import { Attribute, ElementLocation } from 'parse5';
export declare function replaceOpenTag(sourceCodeLocation: ElementLocation, recorder: UpdateRecorder, templateOffset: number, { tag, directive, type }: {
    tag: string;
    directive: string;
    type: string;
}): void;
export declare function replaceSizeAttr(attrs: Attribute[], sourceCodeLocation: ElementLocation, recorder: UpdateRecorder, templateOffset: number): void;
export declare function removeClosingTag(sourceCodeLocation: ElementLocation, recorder: UpdateRecorder, templateOffset: number): void;
export declare function closeStartTag({ startTag }: ElementLocation, recorder: UpdateRecorder, templateOffset: number): void;
