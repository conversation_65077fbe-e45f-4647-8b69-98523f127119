import * as i0 from "@angular/core";
import * as i1 from "./radio-list.component";
import * as i2 from "@angular/common";
import * as i3 from "@angular/forms";
import * as i4 from "@tinkoff/ng-polymorpheus";
import * as i5 from "@taiga-ui/kit/components/radio-group";
import * as i6 from "@taiga-ui/kit/components/radio-labeled";
export declare class TuiRadioListModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRadioListModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiRadioListModule, [typeof i1.TuiRadioListComponent], [typeof i2.CommonModule, typeof i3.FormsModule, typeof i4.PolymorpheusModule, typeof i5.TuiRadioGroupModule, typeof i6.TuiRadioLabeledModule], [typeof i1.TuiRadioListComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiRadioListModule>;
}
