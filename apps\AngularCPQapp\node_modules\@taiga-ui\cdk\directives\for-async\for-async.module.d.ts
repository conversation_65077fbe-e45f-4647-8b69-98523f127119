import * as i0 from "@angular/core";
import * as i1 from "./for-async.directive";
/**
 * @deprecated:
 * remove in v4.0
 */
export declare class TuiForAsyncModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiForAsyncModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiForAsyncModule, [typeof i1.TuiForAsyncDirective], never, [typeof i1.TuiForAsyncDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiForAsyncModule>;
}
