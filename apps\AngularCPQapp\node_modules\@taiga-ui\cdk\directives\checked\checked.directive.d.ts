import { EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export declare class TuiCheckedDirective {
    private indeterminate;
    private checked;
    get isChecked(): boolean;
    get isIndeterminate(): boolean;
    set tuiChecked(checked: boolean | null);
    readonly tuiCheckedChange: EventEmitter<boolean>;
    onChange({ checked }: HTMLInputElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiCheckedDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiCheckedDirective, "input[tuiChecked], input[tuiCheckedChange]", never, { "tuiChecked": "tuiChecked"; }, { "tuiCheckedChange": "tuiCheckedChange"; }, never>;
}
