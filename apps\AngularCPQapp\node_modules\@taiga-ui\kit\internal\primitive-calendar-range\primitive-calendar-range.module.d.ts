import * as i0 from "@angular/core";
import * as i1 from "./primitive-calendar-range.component";
import * as i2 from "@taiga-ui/cdk";
import * as i3 from "@taiga-ui/core";
/**
 * @internal
 */
export declare class TuiPrimitiveCalendarRangeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPrimitiveCalendarRangeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiPrimitiveCalendarRangeModule, [typeof i1.TuiPrimitiveCalendarRangeComponent], [typeof i2.TuiMapperPipeModule, typeof i3.TuiScrollbarModule, typeof i3.TuiCalendarModule], [typeof i1.TuiPrimitiveCalendarRangeComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiPrimitiveCalendarRangeModule>;
}
