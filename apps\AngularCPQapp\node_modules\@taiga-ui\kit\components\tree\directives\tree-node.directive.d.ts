import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { TuiTreeItemComponent } from '../components/tree-item/tree-item.component';
import { TuiTreeAccessor } from '../misc/tree.interfaces';
import * as i0 from "@angular/core";
export declare class TuiTreeNodeDirective<T> implements OnDestroy {
    private readonly directive;
    private readonly component;
    set value(value: T);
    constructor(directive: TuiTreeAccessor<T>, component: TuiTreeItemComponent);
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTreeNodeDirective<any>, [{ optional: true; }, null]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiTreeNodeDirective<any>, "tui-tree-item[tuiTreeNode]", never, { "value": "tuiTreeNode"; }, {}, never>;
}
