import * as i0 from "@angular/core";
import * as i1 from "./input-count.component";
import * as i2 from "./input-count.directive";
import * as i3 from "@angular/common";
import * as i4 from "@angular/forms";
import * as i5 from "@taiga-ui/core";
import * as i6 from "@taiga-ui/kit/directives";
import * as i7 from "@taiga-ui/kit/components/input-number";
/**
 * @deprecated use {@link TuiInputNumberComponent} with [step] instead
 */
export declare class TuiInputCountModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputCountModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputCountModule, [typeof i1.TuiInputCountComponent, typeof i2.TuiInputCountDirective], [typeof i3.CommonModule, typeof i4.FormsModule, typeof i5.TuiButtonModule, typeof i5.TuiPrimitiveTextfieldModule, typeof i5.TuiTextfieldControllerModule, typeof i6.TuiValueAccessorModule, typeof i7.TuiInputNumberModule], [typeof i1.TuiInputCountComponent, typeof i2.TuiInputCountDirective, typeof i5.TuiTextfieldComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputCountModule>;
}
