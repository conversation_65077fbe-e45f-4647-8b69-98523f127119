import * as i0 from "@angular/core";
import * as i1 from "./input-password.component";
import * as i2 from "./input-password.directive";
import * as i3 from "@angular/common";
import * as i4 from "@angular/forms";
import * as i5 from "@tinkoff/ng-polymorpheus";
import * as i6 from "@taiga-ui/core";
export declare class TuiInputPasswordModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputPasswordModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputPasswordModule, [typeof i1.TuiInputPasswordComponent, typeof i2.TuiInputPasswordDirective], [typeof i3.CommonModule, typeof i4.FormsModule, typeof i5.PolymorpheusModule, typeof i6.TuiWrapperModule, typeof i6.TuiSvgModule, typeof i6.TuiHintModule, typeof i6.TuiPrimitiveTextfieldModule, typeof i6.TuiTextfieldControllerModule], [typeof i1.TuiInputPasswordComponent, typeof i2.TuiInputPasswordDirective, typeof i6.TuiTextfieldComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputPasswordModule>;
}
