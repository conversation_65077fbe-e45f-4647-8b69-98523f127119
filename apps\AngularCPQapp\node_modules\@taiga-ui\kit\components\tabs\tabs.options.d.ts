import { Provider } from '@angular/core';
export interface TuiTabsOptions {
    readonly exposeActive: boolean;
    readonly itemsLimit: number;
    readonly minMoreWidth: number;
    readonly underline: boolean;
}
export declare const TUI_TABS_DEFAULT_OPTIONS: TuiTabsOptions;
/**
 * Default parameters for Tabs component
 */
export declare const TUI_TABS_OPTIONS: import("@angular/core").InjectionToken<TuiTabsOptions>;
export declare function tuiTabsOptionsProvider(options: Partial<TuiTabsOptions>): Provider;
