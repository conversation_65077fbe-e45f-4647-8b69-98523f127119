import { ChangeDetectorRef } from '@angular/core';
import { SafeResourceUrl } from '@angular/platform-browser';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiLazyLoadingService extends Observable<SafeResourceUrl | string> {
    private readonly src$;
    constructor(cdr: ChangeDetectorRef, destroy$: Observable<void>, intersections$: Observable<IntersectionObserverEntry[]>);
    next(src: SafeResourceUrl | string): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiLazyLoadingService, [null, { self: true; }, null]>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TuiLazyLoadingService>;
}
