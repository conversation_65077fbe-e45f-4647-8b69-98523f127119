import { PipeTransform } from '@angular/core';
import { TuiCountryIsoCode } from '@taiga-ui/i18n';
import * as i0 from "@angular/core";
export declare class TuiIsoToCountryCodePipe implements PipeTransform {
    private readonly countriesMasks;
    constructor(countriesMasks: Record<TuiCountryIsoCode, string>);
    transform(isoCode: TuiCountryIsoCode): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiIsoToCountryCodePipe, never>;
    static ɵpipe: i0.ɵɵPipeDeclaration<TuiIsoToCountryCodePipe, "tuiIsoToCountryCode">;
}
