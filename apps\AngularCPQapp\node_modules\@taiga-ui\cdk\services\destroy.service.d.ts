import { <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import * as i0 from "@angular/core";
/**
 * @note:
 * Observable abstraction over ngOnDestroy to use with takeUntil
 *
 * Why we use `ReplaySubject` instead of `Subject`?
 * Well, we’ll use ReplaySubject to emit the last message in case
 * the subscription is ended after the component is destroyed.
 */
export declare class TuiDestroyService extends ReplaySubject<void> implements OnDestroy {
    constructor();
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiDestroyService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TuiDestroyService>;
}
