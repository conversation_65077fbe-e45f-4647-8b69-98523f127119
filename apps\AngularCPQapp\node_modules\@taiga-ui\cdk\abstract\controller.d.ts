import { OnChanges } from '@angular/core';
import { Subject } from 'rxjs';
import * as i0 from "@angular/core";
export declare abstract class AbstractTuiController implements OnChanges {
    readonly change$: Subject<void>;
    ngOnChanges(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<AbstractTuiController, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<AbstractTuiController, never, never, {}, {}, never>;
}
