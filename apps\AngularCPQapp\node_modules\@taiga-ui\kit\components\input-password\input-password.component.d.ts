import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TuiContextWithImplicit, TuiFocusableElementAccessor, TuiInputType, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import { TuiBrightness, TuiHintOptionsDirective, TuiSizeL, TuiSizeS, TuiTextfieldSizeDirective } from '@taiga-ui/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { Observable } from 'rxjs';
import { TuiInputPasswordOptions } from './input-password.options';
import * as i0 from "@angular/core";
export declare class TuiInputPasswordComponent extends AbstractTuiControl<string> implements TuiFocusableElementAccessor {
    private readonly textfieldSize;
    readonly passwordTexts$: Observable<[string, string]>;
    readonly options: TuiInputPasswordOptions;
    readonly hintOptions: TuiHintOptionsDirective | null;
    private readonly mode$;
    private readonly textfield?;
    private readonly directive$;
    isPasswordHidden: boolean;
    readonly computedAppearance$: Observable<string>;
    readonly type: TuiContextWithImplicit<TuiSizeL | TuiSizeS>;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, textfieldSize: TuiTextfieldSizeDirective, passwordTexts$: Observable<[string, string]>, options: TuiInputPasswordOptions, hintOptions: TuiHintOptionsDirective | null, mode$: Observable<TuiBrightness | null>);
    get size(): TuiSizeL | TuiSizeS;
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    get icon(): PolymorpheusContent<TuiContextWithImplicit<TuiSizeL | TuiSizeS>>;
    get inputType(): TuiInputType;
    onValueChange(textValue: string): void;
    onFocused(focused: boolean): void;
    togglePasswordVisibility(): void;
    protected getFallbackValue(): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputPasswordComponent, [{ optional: true; self: true; }, null, null, null, null, { optional: true; }, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputPasswordComponent, "tui-input-password", never, {}, {}, never, ["*", "input"]>;
}
