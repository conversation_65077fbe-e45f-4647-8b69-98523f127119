import { Provider } from '@angular/core';
import { TuiSizeL, TuiSizeS } from '@taiga-ui/core';
import { TuiStatus } from '@taiga-ui/kit/types';
export interface TuiTagOptions {
    readonly autoColor: boolean;
    readonly size: TuiSizeL | TuiSizeS;
    readonly status: TuiStatus;
}
/** Default values for the tag options. */
export declare const TUI_TAG_DEFAULT_OPTIONS: TuiTagOptions;
/**
 * Default parameters for Tag component
 */
export declare const TUI_TAG_OPTIONS: import("@angular/core").InjectionToken<TuiTagOptions>;
export declare function tuiTagOptionsProvider(options: Partial<TuiTagOptions>): Provider;
