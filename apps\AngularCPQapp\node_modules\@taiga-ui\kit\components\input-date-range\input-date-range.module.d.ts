import * as i0 from "@angular/core";
import * as i1 from "./input-date-range.component";
import * as i2 from "./input-date-range.directive";
import * as i3 from "@angular/common";
import * as i4 from "@maskito/angular";
import * as i5 from "@taiga-ui/cdk";
import * as i6 from "@tinkoff/ng-polymorpheus";
import * as i7 from "@taiga-ui/core";
import * as i8 from "@taiga-ui/kit/components/calendar-range";
import * as i9 from "@taiga-ui/kit/directives";
export declare class TuiInputDateRangeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateRangeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputDateRangeModule, [typeof i1.TuiInputDateRangeComponent, typeof i2.TuiInputDateRangeDirective], [typeof i3.CommonModule, typeof i4.MaskitoModule, typeof i5.TuiActiveZoneModule, typeof i5.TuiLetModule, typeof i6.PolymorpheusModule, typeof i7.TuiWrapperModule, typeof i7.TuiPrimitiveTextfieldModule, typeof i7.TuiTextfieldControllerModule, typeof i7.TuiHostedDropdownModule, typeof i7.TuiSvgModule, typeof i8.TuiCalendarRangeModule, typeof i9.TuiValueAccessorModule], [typeof i1.TuiInputDateRangeComponent, typeof i2.TuiInputDateRangeDirective, typeof i7.TuiTextfieldComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputDateRangeModule>;
}
