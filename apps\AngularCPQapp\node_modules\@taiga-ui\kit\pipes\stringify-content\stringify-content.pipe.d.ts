import { PipeTransform } from '@angular/core';
import { TuiStringHandler } from '@taiga-ui/cdk';
import { TuiValueContentContext } from '@taiga-ui/core';
import * as i0 from "@angular/core";
export declare class TuiStringifyContentPipe implements PipeTransform {
    transform<T>(stringify: TuiStringHandler<T>): TuiStringHandler<TuiValueContentContext<T>>;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiStringifyContentPipe, never>;
    static ɵpipe: i0.ɵɵPipeDeclaration<TuiStringifyContentPipe, "tuiStringifyContent">;
}
