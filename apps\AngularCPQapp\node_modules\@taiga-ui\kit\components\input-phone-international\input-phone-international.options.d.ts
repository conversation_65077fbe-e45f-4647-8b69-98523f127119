import { Provider } from '@angular/core';
import { TuiCountryIsoCode } from '@taiga-ui/i18n';
export interface TuiInputPhoneInternationalOptions {
    readonly countries: readonly TuiCountryIsoCode[];
    readonly countryIsoCode: TuiCountryIsoCode;
}
export declare const TUI_INPUT_PHONE_INTERNATIONAL_DEFAULT_OPTIONS: TuiInputPhoneInternationalOptions;
/**
 * Default parameters for input phone international component
 */
export declare const TUI_INPUT_PHONE_INTERNATIONAL_OPTIONS: import("@angular/core").InjectionToken<TuiInputPhoneInternationalOptions>;
export declare function tuiInputPhoneInternationalOptionsProvider(options: Partial<TuiInputPhoneInternationalOptions>): Provider;
