import * as i0 from "@angular/core";
import * as i1 from "./prompt.component";
import * as i2 from "@angular/common";
import * as i3 from "@tinkoff/ng-polymorpheus";
import * as i4 from "@taiga-ui/core";
import * as i5 from "@taiga-ui/cdk";
export declare class TuiPromptModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPromptModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiPromptModule, [typeof i1.TuiPromptComponent], [typeof i2.CommonModule, typeof i3.PolymorpheusModule, typeof i4.TuiButtonModule, typeof i5.TuiAutoFocusModule], [typeof i1.TuiPromptComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiPromptModule>;
}
