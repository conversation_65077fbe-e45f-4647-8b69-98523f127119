import * as i0 from "@angular/core";
import * as i1 from "./input-inline.component";
import * as i2 from "@angular/common";
import * as i3 from "@angular/forms";
import * as i4 from "@taiga-ui/cdk";
import * as i5 from "@taiga-ui/core";
export declare class TuiInputInlineModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputInlineModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputInlineModule, [typeof i1.TuiInputInlineComponent], [typeof i2.CommonModule, typeof i3.FormsModule, typeof i4.TuiFocusableModule, typeof i4.TuiFocusedModule, typeof i5.TuiMaskAccessorModule], [typeof i1.TuiInputInlineComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputInlineModule>;
}
