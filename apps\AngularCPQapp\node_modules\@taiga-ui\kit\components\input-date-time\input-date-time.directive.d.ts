import { TuiDay, TuiTime } from '@taiga-ui/cdk';
import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { TuiInputDateTimeComponent } from './input-date-time.component';
import * as i0 from "@angular/core";
export declare class TuiInputDateTimeDirective extends AbstractTuiTextfieldHost<TuiInputDateTimeComponent> {
    get value(): string;
    get rawValue(): [TuiDay | null, TuiTime | null];
    onValueChange(value: string): void;
    writeValue(value: [TuiDay | null, TuiTime | null]): void;
    process(input: HTMLInputElement): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateTimeDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputDateTimeDirective, "tui-input-date-time", never, {}, {}, never>;
}
