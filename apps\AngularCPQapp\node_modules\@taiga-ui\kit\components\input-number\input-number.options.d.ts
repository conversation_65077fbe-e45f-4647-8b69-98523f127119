import { Provider } from '@angular/core';
import { TuiDecimal } from '@taiga-ui/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
export interface TuiInputNumberOptions {
    readonly decimal: TuiDecimal;
    readonly icons: Readonly<{
        down: PolymorpheusContent;
        up: PolymorpheusContent;
    }>;
    readonly max: number;
    readonly min: number;
    readonly precision: number;
    readonly step: number;
}
/** Default values for the input number options. */
export declare const TUI_INPUT_NUMBER_DEFAULT_OPTIONS: TuiInputNumberOptions;
/**
 * Default parameters for input count component
 */
export declare const TUI_INPUT_NUMBER_OPTIONS: import("@angular/core").InjectionToken<TuiInputNumberOptions>;
export declare function tuiInputNumberOptionsProvider(options: Partial<TuiInputNumberOptions>): Provider;
