import { PipeTransform } from '@angular/core';
import { TuiMatcher } from '@taiga-ui/cdk/types';
import * as i0 from "@angular/core";
export declare class TuiFilterPipe implements PipeTransform {
    /**
     * Filters an array through a matcher function using additional arguments
     *
     * @param items array
     * @param matcher method for filtering
     * @param args arbitrary number of additional arguments
     */
    transform<T>(items: readonly T[], matcher: TuiMatcher<T>, ...args: any[]): T[];
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiFilterPipe, never>;
    static ɵpipe: i0.ɵɵPipeDeclaration<TuiFilterPipe, "tuiFilter">;
}
