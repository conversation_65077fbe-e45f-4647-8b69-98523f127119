import * as i0 from "@angular/core";
import * as i1 from "./value-accessor.directive";
export declare class TuiValueAccessorModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiValueAccessorModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiValueAccessorModule, [typeof i1.TuiValueAccessorDirective], never, [typeof i1.TuiValueAccessorDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiValueAccessorModule>;
}
