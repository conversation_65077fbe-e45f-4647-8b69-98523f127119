import { ElementRef } from '@angular/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiDroppableDirective {
    readonly tuiDroppableDropped: Observable<DataTransfer>;
    readonly tuiDroppableDragOverChange: Observable<DataTransfer | null>;
    constructor({ nativeElement }: ElementRef<HTMLElement>, destroy$: Observable<void>);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiDroppableDirective, [null, { self: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiDroppableDirective, "[tuiDroppableDropped], [tuiDroppableDragOverChange]", never, {}, { "tuiDroppableDropped": "tuiDroppableDropped"; "tuiDroppableDragOverChange": "tuiDroppableDragOverChange"; }, never>;
}
