import { AbstractControl, NgControl } from '@angular/forms';
import * as i0 from "@angular/core";
export declare class TuiControlDirective {
    private readonly ngControl;
    constructor(ngControl: NgControl);
    get control(): AbstractControl;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiControlDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiControlDirective, "[tuiControl]", ["ngControl"], {}, {}, never>;
}
