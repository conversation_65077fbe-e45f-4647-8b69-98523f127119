{"name": "angular-cpqapp", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^14.2.0", "@angular/common": "^14.2.0", "@angular/compiler": "^14.2.0", "@angular/core": "^14.2.0", "@angular/forms": "^14.2.0", "@angular/platform-browser": "^14.2.0", "@angular/platform-browser-dynamic": "^14.2.0", "@angular/router": "^14.2.0", "@popperjs/core": "^2.11.8", "@taiga-ui/addon-commerce": "^3.117.0", "@taiga-ui/cdk": "^3.117.0", "@taiga-ui/core": "^3.117.0", "@taiga-ui/kit": "^3.117.0", "bootstrap": "^5.3.6", "rxjs": "~7.5.0", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.13", "@angular/cli": "~14.2.13", "@angular/compiler-cli": "^14.2.0", "@types/jasmine": "~4.0.0", "jasmine-core": "~4.3.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.7.2"}}