import { TuiAppearance, TuiDialogContext } from '@taiga-ui/core';
import { PolymorpheusComponent, PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export interface TuiPromptData {
    readonly content?: PolymorpheusContent;
    readonly no?: string;
    readonly yes?: string;
}
export declare class TuiPromptComponent {
    readonly words$: Observable<{
        no: string;
        yes: string;
    }>;
    readonly context: TuiDialogContext<boolean, TuiPromptData | undefined>;
    private readonly isMobile;
    constructor(words$: Observable<{
        no: string;
        yes: string;
    }>, context: TuiDialogContext<boolean, TuiPromptData | undefined>, isMobile: boolean);
    get appearance(): TuiAppearance;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPromptComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiPromptComponent, "tui-prompt", never, {}, {}, never, never>;
}
export declare const TUI_PROMPT: PolymorpheusComponent<TuiPromptComponent, any>;
