import { EventEmitter } from '@angular/core';
import { tuiIsString } from '@taiga-ui/cdk';
import { TuiCommonIcons } from '@taiga-ui/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiPushComponent {
    readonly closeWord$: Observable<string>;
    readonly icons: TuiCommonIcons;
    heading: string;
    type: string;
    lines: number;
    timestamp: number | string;
    readonly close: EventEmitter<void>;
    readonly isString: typeof tuiIsString;
    constructor(closeWord$: Observable<string>, icons: TuiCommonIcons);
    get closeable(): boolean;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPushComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiPushComponent, "tui-push", never, { "heading": "heading"; "type": "type"; "lines": "lines"; "timestamp": "timestamp"; }, { "close": "close"; }, never, ["img", "tui-svg", "*", "[tuiButton]", "[tuiLink]"]>;
}
