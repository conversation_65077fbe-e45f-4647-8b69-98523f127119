import { ElementRef } from '@angular/core';
import { TuiFocusableElementAccessor, TuiNativeFocusableElement } from '@taiga-ui/cdk/interfaces';
import type { TuiAutofocusHandler } from '../autofocus.options';
import * as i0 from "@angular/core";
export declare abstract class AbstractTuiAutofocusHandler implements TuiAutofocusHandler {
    protected readonly focusable: TuiFocusableElementAccessor | null;
    protected readonly el: ElementRef<HTMLElement>;
    protected constructor(focusable: TuiFocusableElementAccessor | null, el: ElementRef<HTMLElement>);
    protected get element(): TuiNativeFocusableElement;
    protected get isTextFieldElement(): boolean;
    abstract setFocus(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<AbstractTuiAutofocusHandler, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<AbstractTuiAutofocusHandler, never, never, {}, {}, never>;
}
