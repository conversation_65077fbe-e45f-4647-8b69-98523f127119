"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TUI_INTERACTIVE_SELECTORS = void 0;
exports.TUI_INTERACTIVE_SELECTORS = [
    'tui-primitive-textfield',
    'tui-primitive-spin-button',
    'tui-action',
    'tui-input-range',
    'tui-pagination',
    'tui-accordion-item',
    'tui-button',
    'tuiButton',
    'tuiIconButton',
    'tui-toggle',
    'tuiSlider',
    'tui-slider',
    'tui-rating',
    'tui-input-card',
    'tui-input-cvc',
    'tui-input-expire',
    'tui-editor',
    'tui-input-color',
    'tui-input-copy',
    'tui-input-count',
    'tui-input-date-time',
    'tui-input-inline',
    'tui-input-password',
    'tui-input-phone-international',
    'tui-input-phone',
    'tui-input-range',
    'tui-input-slider',
    'tui-input',
    'tui-range',
    'tui-text-area',
    'tui-radio-block',
    'tui-radio-labeled',
    'tui-radio-list',
    'tui-radio',
    'tui-select',
    'tui-input-card-grouped',
    'tui-checkbox-block',
    'tui-checkbox-labeled',
    'tui-combo-box',
    'tui-input-date-range',
    'tui-input-file',
    'tui-input-files',
    'tui-input-month-range',
    'tui-input-month',
    'tui-input-number',
    'tui-input-time',
    'tui-input-time',
    'tui-multi-select',
    'tui-filter',
    'tui-input-tag',
];
