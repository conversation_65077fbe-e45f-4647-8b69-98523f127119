import { AbstractTuiPortalHostComponent } from '@taiga-ui/cdk/abstract';
import * as i0 from "@angular/core";
/**
 * Host element for dynamically created portals, for example using {@link TuiDropdownDirective}.
 */
export declare class TuiDropdownHostComponent extends AbstractTuiPortalHostComponent {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiDropdownHostComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiDropdownHostComponent, "tui-dropdown-host", never, {}, {}, never, ["*"]>;
}
