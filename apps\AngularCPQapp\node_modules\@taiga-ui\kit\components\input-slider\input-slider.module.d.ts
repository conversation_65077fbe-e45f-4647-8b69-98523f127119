import * as i0 from "@angular/core";
import * as i1 from "./input-slider.component";
import * as i2 from "@angular/common";
import * as i3 from "@angular/forms";
import * as i4 from "@tinkoff/ng-polymorpheus";
import * as i5 from "@taiga-ui/cdk";
import * as i6 from "@taiga-ui/kit/components/input-number";
import * as i7 from "@taiga-ui/kit/components/slider";
import * as i8 from "@taiga-ui/core";
export declare class TuiInputSliderModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputSliderModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputSliderModule, [typeof i1.TuiInputSliderComponent], [typeof i2.CommonModule, typeof i3.FormsModule, typeof i4.PolymorpheusModule, typeof i5.TuiFocusableModule, typeof i6.TuiInputNumberModule, typeof i7.TuiSliderModule, typeof i8.TuiTextfieldControllerModule], [typeof i1.TuiInputSliderComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputSliderModule>;
}
