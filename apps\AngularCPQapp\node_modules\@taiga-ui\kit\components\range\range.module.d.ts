import * as i0 from "@angular/core";
import * as i1 from "./range.component";
import * as i2 from "./range-change.directive";
import * as i3 from "@angular/common";
import * as i4 from "@taiga-ui/cdk";
import * as i5 from "@taiga-ui/kit/components/slider";
import * as i6 from "@angular/forms";
export declare class TuiRangeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRangeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiRangeModule, [typeof i1.TuiRangeComponent, typeof i2.TuiRangeChangeDirective], [typeof i3.CommonModule, typeof i4.TuiFocusableModule, typeof i5.TuiSliderModule, typeof i6.FormsModule], [typeof i1.TuiRangeComponent, typeof i2.TuiRangeChangeDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiRangeModule>;
}
