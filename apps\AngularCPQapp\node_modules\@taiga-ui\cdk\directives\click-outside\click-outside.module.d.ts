import * as i0 from "@angular/core";
import * as i1 from "./click-outside.directive";
export declare class TuiClickOutsideModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiClickOutsideModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiClickOutsideModule, [typeof i1.TuiClickOutsideDirective], never, [typeof i1.TuiClickOutsideDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiClickOutsideModule>;
}
