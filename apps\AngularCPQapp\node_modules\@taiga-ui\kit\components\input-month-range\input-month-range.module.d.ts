import * as i0 from "@angular/core";
import * as i1 from "./input-month-range.component";
import * as i2 from "./input-month-range.directive";
import * as i3 from "@angular/common";
import * as i4 from "@taiga-ui/kit/components/calendar-month";
import * as i5 from "@taiga-ui/core";
import * as i6 from "@taiga-ui/cdk";
export declare class TuiInputMonthRangeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputMonthRangeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiInputMonthRangeModule, [typeof i1.TuiInputMonthRangeComponent, typeof i2.TuiInputMonthRangeDirective], [typeof i3.CommonModule, typeof i4.TuiCalendarMonthModule, typeof i5.TuiHostedDropdownModule, typeof i5.TuiPrimitiveTextfieldModule, typeof i5.TuiSvgModule, typeof i6.TuiPreventDefaultModule, typeof i6.TuiActiveZoneModule, typeof i6.TuiMapperPipeModule, typeof i5.TuiTextfieldControllerModule], [typeof i1.TuiInputMonthRangeComponent, typeof i2.TuiInputMonthRangeDirective, typeof i5.TuiTextfieldComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiInputMonthRangeModule>;
}
