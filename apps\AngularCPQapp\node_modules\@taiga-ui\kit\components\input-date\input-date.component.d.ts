import { ChangeDetectorRef, Type } from '@angular/core';
import { NgControl } from '@angular/forms';
import { MaskitoOptions } from '@maskito/core';
import { AbstractTuiNullableControl, AbstractTuiValueTransformer, TuiActiveZoneDirective, TuiBooleanHandler, TuiContextWithImplicit, TuiDateMode, TuiDay, TuiFocusableElementAccessor, TuiMonth } from '@taiga-ui/cdk';
import { TuiMarkerHandler, TuiSizeL, TuiSizeS, TuiTextfieldSizeDirective, TuiWithOptionalMinMax } from '@taiga-ui/core';
import { TuiNamedDay } from '@taiga-ui/kit/classes';
import { TuiInputDateOptions } from '@taiga-ui/kit/tokens';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiInputDateComponent extends AbstractTuiNullableControl<TuiDay> implements TuiWithOptionalMinMax<TuiDay>, TuiFocusableElementAccessor {
    readonly isMobile: boolean;
    private readonly mobileCalendar;
    readonly dateFormat: TuiDateMode;
    readonly dateSeparator: string;
    readonly dateTexts$: Observable<Record<TuiDateMode, string>>;
    readonly valueTransformer: AbstractTuiValueTransformer<TuiDay | null> | null;
    private readonly options;
    private readonly textfieldSize;
    private readonly textfield?;
    private month;
    min: TuiDay | null;
    max: TuiDay | null;
    disabledItemHandler: TuiBooleanHandler<TuiDay>;
    markerHandler: TuiMarkerHandler;
    items: readonly TuiNamedDay[];
    defaultActiveYearMonth: TuiMonth;
    open: boolean;
    readonly type: TuiContextWithImplicit<TuiActiveZoneDirective>;
    readonly filler$: Observable<string>;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, isMobile: boolean, mobileCalendar: Type<Record<string, any>> | null, dateFormat: TuiDateMode, dateSeparator: string, dateTexts$: Observable<Record<TuiDateMode, string>>, valueTransformer: AbstractTuiValueTransformer<TuiDay | null> | null, options: TuiInputDateOptions, textfieldSize: TuiTextfieldSizeDirective);
    get size(): TuiSizeL | TuiSizeS;
    get computedMin(): TuiDay;
    get computedMax(): TuiDay;
    get nativeFocusableElement(): HTMLInputElement | null;
    get focused(): boolean;
    /**
     * @deprecated
     */
    get computedMobile(): boolean;
    get nativePicker(): boolean;
    get calendarIcon(): TuiInputDateOptions['icon'];
    get computedValue(): string;
    get computedActiveYearMonth(): TuiMonth;
    get nativeValue(): string;
    set nativeValue(value: string);
    get computedMask(): MaskitoOptions;
    get activeItem(): TuiNamedDay | null;
    onClick(): void;
    getComputedFiller(filler: string): string;
    /**
     * TODO: Remove in 4.0
     * @deprecated: use {@link onIconClick} instead
     */
    onMobileClick(): void;
    onIconClick(): void;
    onValueChange(value: string): void;
    onDayClick(value: TuiDay): void;
    onMonthChange(month: TuiMonth): void;
    onOpenChange(open: boolean): void;
    onFocused(focused: boolean): void;
    setDisabledState(): void;
    writeValue(value: TuiDay | null): void;
    protected valueIdenticalComparator(oldValue: TuiDay | null, newValue: TuiDay | null): boolean;
    private computeMaskOptions;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputDateComponent, [{ optional: true; self: true; }, null, null, { optional: true; }, null, null, null, { optional: true; }, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputDateComponent, "tui-input-date:not([multiple])", never, { "min": "min"; "max": "max"; "disabledItemHandler": "disabledItemHandler"; "markerHandler": "markerHandler"; "items": "items"; "defaultActiveYearMonth": "defaultActiveYearMonth"; }, {}, never, ["*", "input"]>;
}
