import { Provider } from '@angular/core';
import { TuiValueContentContext } from '@taiga-ui/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
export interface TuiSelectOptions<T> {
    readonly valueContent: PolymorpheusContent<TuiValueContentContext<T>>;
}
export declare const TUI_SELECT_DEFAULT_OPTIONS: TuiSelectOptions<unknown>;
/**
 * Default parameters for Select component
 */
export declare const TUI_SELECT_OPTIONS: import("@angular/core").InjectionToken<TuiSelectOptions<unknown>>;
export declare function tuiSelectOptionsProvider<T>(options: Partial<TuiSelectOptions<T>>): Provider;
