import { inject } from '@angular/core';
import { WINDOW } from '@ng-web-apis/common';
import { tuiCreateTokenFromFactory } from '@taiga-ui/cdk/utils';
export const TUI_TOUCH_SUPPORTED = tuiCreateTokenFromFactory(() => inject(WINDOW).matchMedia('(any-pointer: coarse)').matches);
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidG91Y2gtc3VwcG9ydGVkLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vcHJvamVjdHMvY2RrL3Rva2Vucy90b3VjaC1zdXBwb3J0ZWQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsT0FBTyxFQUFDLE1BQU0sRUFBQyxNQUFNLGVBQWUsQ0FBQztBQUNyQyxPQUFPLEVBQUMsTUFBTSxFQUFDLE1BQU0scUJBQXFCLENBQUM7QUFDM0MsT0FBTyxFQUFDLHlCQUF5QixFQUFDLE1BQU0scUJBQXFCLENBQUM7QUFFOUQsTUFBTSxDQUFDLE1BQU0sbUJBQW1CLEdBQUcseUJBQXlCLENBQ3hELEdBQUcsRUFBRSxDQUFDLE1BQU0sQ0FBQyxNQUFNLENBQUMsQ0FBQyxVQUFVLENBQUMsdUJBQXVCLENBQUMsQ0FBQyxPQUFPLENBQ25FLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge2luamVjdH0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XG5pbXBvcnQge1dJTkRPV30gZnJvbSAnQG5nLXdlYi1hcGlzL2NvbW1vbic7XG5pbXBvcnQge3R1aUNyZWF0ZVRva2VuRnJvbUZhY3Rvcnl9IGZyb20gJ0B0YWlnYS11aS9jZGsvdXRpbHMnO1xuXG5leHBvcnQgY29uc3QgVFVJX1RPVUNIX1NVUFBPUlRFRCA9IHR1aUNyZWF0ZVRva2VuRnJvbUZhY3RvcnkoXG4gICAgKCkgPT4gaW5qZWN0KFdJTkRPVykubWF0Y2hNZWRpYSgnKGFueS1wb2ludGVyOiBjb2Fyc2UpJykubWF0Y2hlcyxcbik7XG4iXX0=