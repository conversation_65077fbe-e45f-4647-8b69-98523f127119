import { tap } from 'rxjs/operators';
export function tuiWatch(cdr) {
    return tap(() => {
        cdr.markForCheck();
    });
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoid2F0Y2guanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi9wcm9qZWN0cy9jZGsvb2JzZXJ2YWJsZXMvd2F0Y2gudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBRUEsT0FBTyxFQUFDLEdBQUcsRUFBQyxNQUFNLGdCQUFnQixDQUFDO0FBRW5DLE1BQU0sVUFBVSxRQUFRLENBQUksR0FBc0I7SUFDOUMsT0FBTyxHQUFHLENBQUMsR0FBRyxFQUFFO1FBQ1osR0FBRyxDQUFDLFlBQVksRUFBRSxDQUFDO0lBQ3ZCLENBQUMsQ0FBQyxDQUFDO0FBQ1AsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Q2hhbmdlRGV0ZWN0b3JSZWZ9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xuaW1wb3J0IHtNb25vVHlwZU9wZXJhdG9yRnVuY3Rpb259IGZyb20gJ3J4anMnO1xuaW1wb3J0IHt0YXB9IGZyb20gJ3J4anMvb3BlcmF0b3JzJztcblxuZXhwb3J0IGZ1bmN0aW9uIHR1aVdhdGNoPFQ+KGNkcjogQ2hhbmdlRGV0ZWN0b3JSZWYpOiBNb25vVHlwZU9wZXJhdG9yRnVuY3Rpb248VD4ge1xuICAgIHJldHVybiB0YXAoKCkgPT4ge1xuICAgICAgICBjZHIubWFya0ZvckNoZWNrKCk7XG4gICAgfSk7XG59XG4iXX0=