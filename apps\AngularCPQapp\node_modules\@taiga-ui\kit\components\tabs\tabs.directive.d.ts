import { AfterViewChecked, ElementRef, EventEmitter } from '@angular/core';
import * as i0 from "@angular/core";
export declare class TuiTabsDirective implements AfterViewChecked {
    private readonly el;
    activeItemIndex: number;
    readonly activeItemIndexChange: EventEmitter<number>;
    constructor(el: ElementRef<HTMLElement>);
    get tabs(): readonly HTMLElement[];
    get activeElement(): HTMLElement | null;
    onActivate(event: Event, element: HTMLElement): void;
    moveFocus(current: HTMLElement, step: number): void;
    ngAfterViewChecked(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTabsDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiTabsDirective, "tui-tabs, nav[tuiTabs]", never, { "activeItemIndex": "activeItemIndex"; }, { "activeItemIndexChange": "activeItemIndexChange"; }, never>;
}
