import { ChangeDetectorRef, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { TuiDialog } from '@taiga-ui/cdk/types';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
/**
 * Is closing dialog on browser backward navigation enabled
 */
export declare const TUI_DIALOG_CLOSES_ON_BACK: import("@angular/core").InjectionToken<Observable<boolean>>;
export declare class TuiDialogHostComponent<T extends TuiDialog<unknown, unknown>> implements OnInit {
    readonly isMobile: boolean;
    readonly isDialogClosesOnBack$: Observable<boolean>;
    private readonly dialogsByType;
    private readonly historyRef;
    private readonly titleService;
    private readonly destroy$;
    private readonly cdr;
    private readonly doc;
    dialogs: readonly T[];
    constructor(isMobile: boolean, isDialogClosesOnBack$: Observable<boolean>, dialogsByType: Array<Observable<readonly T[]>>, historyRef: History, titleService: Title, destroy$: Observable<void>, cdr: ChangeDetectorRef, doc: Document);
    ngOnInit(): void;
    closeLast(dialogs: readonly T[], isDialogClosesOnBack: boolean): void;
    onDialog({ propertyName }: TransitionEvent, popupOpened: boolean, isDialogClosesOnBack: boolean): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiDialogHostComponent<any>, [null, null, null, null, null, { self: true; }, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiDialogHostComponent<any>, "tui-dialog-host", never, {}, {}, never, never>;
}
