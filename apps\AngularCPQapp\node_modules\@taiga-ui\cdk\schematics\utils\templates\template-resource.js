"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTemplateOffset = exports.getPathFromTemplateResource = exports.getTemplateFromTemplateResource = void 0;
function getTemplateFromTemplateResource(templateRes, fileSystem) {
    var _a;
    if ('template' in templateRes) {
        return templateRes.template;
    }
    const path = fileSystem.resolve(templateRes.templatePath);
    return (_a = fileSystem.read(path)) !== null && _a !== void 0 ? _a : '';
}
exports.getTemplateFromTemplateResource = getTemplateFromTemplateResource;
function getPathFromTemplateResource(templateRes) {
    if ('templatePath' in templateRes) {
        return templateRes.templatePath;
    }
    return templateRes.componentPath;
}
exports.getPathFromTemplateResource = getPathFromTemplateResource;
function getTemplateOffset(templateRes) {
    return 'offset' in templateRes ? templateRes.offset : 0;
}
exports.getTemplateOffset = getTemplateOffset;
