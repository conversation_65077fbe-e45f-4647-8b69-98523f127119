import * as i0 from "@angular/core";
import * as i1 from "./tag.component";
import * as i2 from "@angular/common";
import * as i3 from "@angular/forms";
import * as i4 from "@taiga-ui/core";
import * as i5 from "@tinkoff/ng-polymorpheus";
export declare class TuiTagModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTagModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiTagModule, [typeof i1.TuiTagComponent], [typeof i2.CommonModule, typeof i3.FormsModule, typeof i4.TuiSvgModule, typeof i4.TuiLoaderModule, typeof i5.PolymorpheusModule], [typeof i1.TuiTagComponent]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiTagModule>;
}
