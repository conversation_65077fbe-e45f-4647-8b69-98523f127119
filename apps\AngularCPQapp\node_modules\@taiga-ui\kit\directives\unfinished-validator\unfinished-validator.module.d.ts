import * as i0 from "@angular/core";
import * as i1 from "./unfinished-validator.directive";
export declare class TuiUnfinishedValidatorModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiUnfinishedValidatorModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiUnfinishedValidatorModule, [typeof i1.TuiUnfinishedValidatorDirective], never, [typeof i1.TuiUnfinishedValidatorDirective]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiUnfinishedValidatorModule>;
}
