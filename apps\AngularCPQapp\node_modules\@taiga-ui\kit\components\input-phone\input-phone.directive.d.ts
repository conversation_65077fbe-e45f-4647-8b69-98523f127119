import { DoCheck } from '@angular/core';
import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { TuiInputPhoneComponent } from './input-phone.component';
import * as i0 from "@angular/core";
export declare class TuiInputPhoneDirective extends AbstractTuiTextfieldHost<TuiInputPhoneComponent> implements DoCheck {
    input?: HTMLInputElement;
    get value(): string;
    onValueChange(value: string): void;
    process(input: HTMLInputElement): void;
    ngDoCheck(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputPhoneDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputPhoneDirective, "tui-input-phone", never, {}, {}, never>;
}
