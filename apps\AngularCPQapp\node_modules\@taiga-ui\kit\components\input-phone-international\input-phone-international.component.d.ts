import { ChangeDetectorRef, EventEmitter } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TuiContextWithImplicit, TuiFocusableElementAccessor, TuiTypedMapper } from '@taiga-ui/cdk';
import { TuiFlagPipe, TuiSizeL, TuiSizeM, TuiSizeS, TuiTextfieldSizeDirective } from '@taiga-ui/core';
import { TuiCountryIsoCode } from '@taiga-ui/i18n';
import { TuiToCountryCodePipe } from '@taiga-ui/kit/pipes';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { Observable } from 'rxjs';
import { TuiInputPhoneInternationalOptions } from './input-phone-international.options';
import * as i0 from "@angular/core";
export declare class TuiInputPhoneInternationalComponent extends AbstractTuiControl<string> implements TuiFocusableElementAccessor {
    readonly countriesNames$: Observable<Record<TuiCountryIsoCode, string>>;
    readonly countriesMasks: Record<TuiCountryIsoCode, string>;
    private readonly options;
    private readonly flagPipe;
    private readonly extractCountryCodePipe;
    private readonly textfieldSize;
    private readonly inputPhoneComponent?;
    private readonly primitiveTextfield?;
    set isoCode(code: TuiCountryIsoCode);
    countries: readonly TuiCountryIsoCode[];
    readonly countryIsoCodeChange: EventEmitter<TuiCountryIsoCode>;
    countryIsoCode: TuiCountryIsoCode;
    open: boolean;
    readonly arrow: PolymorpheusContent<TuiContextWithImplicit<TuiSizeL | TuiSizeM | TuiSizeS>>;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, countriesNames$: Observable<Record<TuiCountryIsoCode, string>>, countriesMasks: Record<TuiCountryIsoCode, string>, options: TuiInputPhoneInternationalOptions, flagPipe: TuiFlagPipe, extractCountryCodePipe: TuiToCountryCodePipe, textfieldSize: TuiTextfieldSizeDirective);
    get size(): TuiSizeL | TuiSizeS;
    get nativeFocusableElement(): HTMLElement | null;
    get focused(): boolean;
    get inputPhoneCountryCode(): string;
    get phoneMaskAfterCountryCode(): string;
    /**
     * @deprecated use `<img [src]="countryIsoCode | tuiFlagPipe" />`
     * TODO drop in v4.0
     */
    get countryFlagPath(): string;
    onPaste(event: ClipboardEvent | DragEvent): void;
    readonly isoToCountryCodeMapper: TuiTypedMapper<[TuiCountryIsoCode], string>;
    /**
     * @deprecated use `<img [src]="countryIsoCode | tuiFlagPipe" />`
     * TODO drop in v4.0
     */
    getFlagPath(code: TuiCountryIsoCode): string;
    onItemClick(isoCode: TuiCountryIsoCode): void;
    setDisabledState(): void;
    /**
     * @deprecated use `{{ countryIsoCode | tuiIsoToCountryCode }}`
     * TODO drop in v4.0
     */
    isoToCountryCode(isoCode: TuiCountryIsoCode): string;
    /** @deprecated use 'value' setter */
    onModelChange(value: string): void;
    onActiveZone(active: boolean): void;
    protected getFallbackValue(): string;
    private calculateMaskAfterCountryCode;
    private close;
    private updateCountryIsoCode;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputPhoneInternationalComponent, [{ optional: true; self: true; }, null, null, null, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputPhoneInternationalComponent, "tui-input-phone-international", never, { "isoCode": "countryIsoCode"; "countries": "countries"; }, { "countryIsoCodeChange": "countryIsoCodeChange"; }, never, ["*"]>;
}
