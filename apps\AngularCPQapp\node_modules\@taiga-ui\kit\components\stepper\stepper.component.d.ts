import { ChangeDetectorRef, ElementRef, EventEmitter } from '@angular/core';
import { TuiScrollService } from '@taiga-ui/cdk';
import { TuiOrientation } from '@taiga-ui/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiStepperComponent {
    private readonly cdr;
    private readonly el;
    private readonly scrollService;
    private readonly duration;
    private readonly destroy$;
    private readonly steps;
    orientation: TuiOrientation;
    set activeIndex(index: number);
    readonly activeItemIndexChange: EventEmitter<number>;
    activeItemIndex: number;
    constructor(cdr: ChangeDetectorRef, el: ElementRef<HTMLElement>, scrollService: TuiScrollService, resize$: Observable<void>, duration: number, destroy$: Observable<void>);
    get changes$(): Observable<unknown>;
    onHorizontal(event: Event, step: number): void;
    onVertical(event: Event, step: number): void;
    indexOf(step: HTMLElement): number;
    isActive(index: number): boolean;
    activate(index: number): void;
    private moveFocus;
    private scrollIntoView;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiStepperComponent, [null, null, null, null, null, { self: true; }]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiStepperComponent, "tui-stepper, nav[tuiStepper]", never, { "orientation": "orientation"; "activeIndex": "activeItemIndex"; }, { "activeItemIndexChange": "activeItemIndexChange"; }, ["steps"], ["*"]>;
}
