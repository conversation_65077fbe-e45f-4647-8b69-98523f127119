{"$schema": "../../../node_modules/@angular-devkit/schematics/collection-schema.json", "schematics": {"ng-add": {"description": "Add Taiga UI to the project.", "factory": "./ng-add/index#ngAdd", "schema": "./ng-add/schema.json"}, "ng-add-setup-project": {"private": true, "description": "Sets up the project after dependencies have been installed", "factory": "./ng-add/setup-project", "schema": "./ng-add/schema.json"}, "updateToV3": {"private": true, "description": "Manually run migration to update Taiga to v3.x.x", "factory": "./ng-update/v3/index#updateToV3", "schema": "./ng-update/v3/schema.json"}, "updateToV3_78": {"private": true, "description": "Manually run migration to update proprietary icons", "factory": "./ng-update/v3-78/index#updateToV3_78", "schema": "./ng-update/v3-78/schema.json"}}}