import { DoCheck } from '@angular/core';
import { AbstractTuiTextfieldHost } from '@taiga-ui/core';
import { TuiInputNumberComponent } from './input-number.component';
import * as i0 from "@angular/core";
export declare class TuiInputNumberDirective extends AbstractTuiTextfieldHost<TuiInputNumberComponent> implements DoCheck {
    input?: HTMLInputElement;
    get value(): string;
    onValueChange(value: string): void;
    ngDoCheck(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputNumberDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiInputNumberDirective, "tui-input-number", never, {}, {}, never>;
}
