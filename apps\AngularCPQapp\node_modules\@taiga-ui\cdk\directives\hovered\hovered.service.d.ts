import { ElementRef, Ng<PERSON>one } from '@angular/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiHoveredService extends Observable<boolean> {
    private readonly el;
    private readonly zone;
    private readonly stream$;
    constructor(el: ElementRef<Element>, zone: NgZone);
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiHoveredService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TuiHoveredService>;
}
