import { AbstractTuiDialogService } from '@taiga-ui/cdk';
import { PolymorpheusComponent } from '@tinkoff/ng-polymorpheus';
import { TuiPushOptions } from './push.options';
import { TuiPushAlertComponent } from './push-alert.component';
import * as i0 from "@angular/core";
export declare class TuiPushService extends AbstractTuiDialogService<TuiPushOptions, string> {
    protected readonly component: PolymorpheusComponent<TuiPushAlertComponent, any>;
    protected readonly defaultOptions: TuiPushOptions;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPushService, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<TuiPushService>;
}
