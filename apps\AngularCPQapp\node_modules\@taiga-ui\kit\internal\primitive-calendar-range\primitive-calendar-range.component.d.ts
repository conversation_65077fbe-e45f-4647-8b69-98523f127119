import { ChangeDetectorRef, EventEmitter, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { TuiBooleanHandler, TuiDay, TuiDayRange, TuiDestroyService, TuiMonth, TuiTypedMapper } from '@taiga-ui/cdk';
import { Tui<PERSON>arkerHandler } from '@taiga-ui/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
/**
 * @internal
 */
export declare class TuiPrimitiveCalendarRangeComponent implements OnInit, OnChanges {
    disabledItemHandler: TuiBooleanHandler<TuiDay>;
    markerHandler: TuiMarkerHandler;
    defaultViewedMonthFirst: TuiMonth;
    defaultViewedMonthSecond: TuiMonth;
    min: TuiDay;
    max: TuiDay;
    value: TuiDayRange | null;
    readonly dayClick: EventEmitter<TuiDay>;
    hoveredItem: TuiDay | null;
    userViewedMonthFirst: TuiMonth;
    userViewedMonthSecond: TuiMonth;
    constructor(valueChanges: Observable<TuiDayRange | null> | null, cdr: ChangeDetectorRef, destroy$: TuiDestroyService);
    get cappedUserViewedMonthSecond(): TuiMonth;
    get cappedUserViewedMonthFirst(): TuiMonth;
    monthOffset: TuiTypedMapper<[TuiMonth, number], TuiMonth>;
    ngOnChanges({ defaultViewedMonthFirst, defaultViewedMonthSecond, }: SimpleChanges): void;
    ngOnInit(): void;
    onSectionFirstViewedMonth(month: TuiMonth): void;
    onSectionSecondViewedMonth(month: TuiMonth): void;
    onDayClick(day: TuiDay): void;
    private setInitialMonths;
    private updatedViewedMonthSecond;
    private updatedViewedMonthFirst;
    private updateViewedMonths;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiPrimitiveCalendarRangeComponent, [{ optional: true; }, null, { self: true; }]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiPrimitiveCalendarRangeComponent, "tui-primitive-calendar-range", never, { "disabledItemHandler": "disabledItemHandler"; "markerHandler": "markerHandler"; "defaultViewedMonthFirst": "defaultViewedMonthFirst"; "defaultViewedMonthSecond": "defaultViewedMonthSecond"; "min": "min"; "max": "max"; "value": "value"; }, { "dayClick": "dayClick"; }, never, never>;
}
