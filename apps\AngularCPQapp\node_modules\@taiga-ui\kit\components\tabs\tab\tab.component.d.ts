import { ElementRef, OnD<PERSON>roy } from '@angular/core';
import { RouterLinkActive } from '@angular/router';
import { TuiBrightness } from '@taiga-ui/core';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiTabComponent implements OnD<PERSON>roy {
    private readonly routerLinkActive;
    private readonly el;
    readonly mode$: Observable<TuiBrightness | null>;
    readonly event$: Observable<Event>;
    readonly margin: number;
    constructor(routerLinkActive: RouterLinkActive | null, el: ElementRef<HTMLElement>, mode$: Observable<TuiBrightness | null>, event$: Observable<Event>, margin: number);
    get isActive(): boolean;
    ngOnDestroy(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTabComponent, [{ optional: true; }, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiTabComponent, "a[tuiTab]:not([routerLink]), a[tuiTab][routerLink][routerLinkActive], button[tuiTab]", never, {}, {}, never, ["*"]>;
}
