import { InjectionToken } from '@angular/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import type { TuiTreeAccessor, Tui<PERSON>reeItemContext, TuiTreeLoader } from './tree.interfaces';
/**
 * Controller for tracking value - TuiTreeItemComponent pairs
 */
export declare const TUI_TREE_ACCESSOR: InjectionToken<TuiTreeAccessor<unknown>>;
/**
 * Controller for expanding the tree
 */
export declare const TUI_TREE_CONTROLLER: InjectionToken<import("./tree.interfaces").TuiTreeController>;
/**
 * A node of a tree view
 */
export declare const TUI_TREE_NODE: InjectionToken<unknown>;
/**
 * A tree node placeholder for loading
 */
export declare const TUI_TREE_LOADING: InjectionToken<{}>;
/**
 * A tree node starting point
 */
export declare const TUI_TREE_START: InjectionToken<unknown>;
/**
 * A service to load tree progressively
 */
export declare const TUI_TREE_LOADER: InjectionToken<TuiTreeLoader<unknown>>;
/**
 * Content for a tree item
 */
export declare const TUI_TREE_CONTENT: InjectionToken<PolymorpheusContent<TuiTreeItemContext>>;
/**
 * Nesting level of current TreeView node
 */
export declare const TUI_TREE_LEVEL: InjectionToken<number>;
