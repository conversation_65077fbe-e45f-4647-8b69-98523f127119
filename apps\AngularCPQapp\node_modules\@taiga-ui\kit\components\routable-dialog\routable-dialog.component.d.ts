import { Injector } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TuiDestroyService } from '@taiga-ui/cdk';
import { TuiDialogService } from '@taiga-ui/core';
import * as i0 from "@angular/core";
export declare class TuiRoutableDialogComponent {
    private readonly route;
    private readonly router;
    private readonly initialUrl;
    constructor(route: ActivatedRoute, router: Router, dialogs: TuiDialogService, injector: Injector, destroy$: TuiDestroyService);
    private get lazyLoadedBackUrl();
    private onDialogClosing;
    private navigateToParent;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRoutableDialogComponent, [null, null, null, null, { self: true; }]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiRoutableDialogComponent, "tui-routable-dialog", never, {}, {}, never, never>;
}
