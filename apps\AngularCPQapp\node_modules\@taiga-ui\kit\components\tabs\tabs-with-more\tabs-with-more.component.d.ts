import { AfterViewInit, ChangeDetectorRef, ElementRef, EventEmitter, QueryList, TemplateRef } from '@angular/core';
import { TuiActiveZoneDirective, TuiContextWithImplicit } from '@taiga-ui/cdk';
import { TuiArrowOptions } from '@taiga-ui/kit/components/arrow';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { Observable } from 'rxjs';
import { TuiTabsOptions } from '../tabs.options';
import * as i0 from "@angular/core";
export declare class TuiTabsWithMoreComponent implements AfterViewInit {
    private readonly options;
    private readonly margin;
    private readonly refresh$;
    private readonly el;
    private readonly cdr;
    readonly moreWord$: Observable<string>;
    readonly arrowOptions: TuiArrowOptions;
    private readonly moreButton?;
    private maxIndex;
    moreContent: PolymorpheusContent;
    dropdownContent: PolymorpheusContent<TuiContextWithImplicit<TuiActiveZoneDirective>>;
    underline: boolean;
    set itemIndex(activeItemIndex: number);
    itemsLimit: number;
    readonly activeItemIndexChange: EventEmitter<number>;
    readonly items: QueryList<TemplateRef<Record<string, unknown>>>;
    activeItemIndex: number;
    open: boolean;
    constructor(options: TuiTabsOptions, margin: number, refresh$: Observable<unknown>, el: ElementRef<HTMLElement>, cdr: ChangeDetectorRef, moreWord$: Observable<string>, arrowOptions: TuiArrowOptions);
    get tabs(): readonly HTMLElement[];
    get activeElement(): HTMLElement | null;
    get isMoreAlone(): boolean;
    get isMoreVisible(): boolean;
    get isMoreFocusable(): boolean;
    get isMoreActive(): boolean;
    get lastVisibleIndex(): number;
    ngAfterViewInit(): void;
    onActiveItemIndexChange(activeItemIndex: number): void;
    onClick(index: number): void;
    onArrowRight(event: Event): void;
    onArrowLeft(): void;
    onWrapperArrow(event: Event, wrapper: HTMLElement, previous: boolean): void;
    isOverflown(index: number): boolean;
    shouldShow(index: number): boolean;
    private focusMore;
    private getMaxIndex;
    private updateActiveItemIndex;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiTabsWithMoreComponent, never>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiTabsWithMoreComponent, "tui-tabs-with-more, nav[tuiTabsWithMore]", never, { "moreContent": "moreContent"; "dropdownContent": "dropdownContent"; "underline": "underline"; "itemIndex": "activeItemIndex"; "itemsLimit": "itemsLimit"; }, { "activeItemIndexChange": "activeItemIndexChange"; }, ["items"], never>;
}
