import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TuiFocusableElementAccessor } from '@taiga-ui/cdk';
import { TuiRatingOptions } from './rating.options';
import * as i0 from "@angular/core";
export declare class TuiRatingComponent extends AbstractTuiControl<number> implements TuiFocusableElementAccessor {
    private readonly options;
    private readonly focusableElement?;
    min: number;
    max: number;
    iconNormal: string;
    iconFilled: string;
    constructor(ngControl: NgControl | null, cdr: ChangeDetectorRef, options: TuiRatingOptions);
    get nativeFocusableElement(): HTMLInputElement | null;
    get focused(): boolean;
    get isFocusable(): boolean;
    get percent(): number;
    onFocused(focused: boolean): void;
    setRateByReverseIndex(index: number): void;
    setRate(value: number): void;
    protected getFallbackValue(): number;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiRatingComponent, [{ optional: true; self: true; }, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiRatingComponent, "tui-rating", never, { "min": "min"; "max": "max"; "iconNormal": "iconNormal"; "iconFilled": "iconFilled"; }, {}, never, ["*"]>;
}
