import * as i0 from "@angular/core";
import * as i1 from "./iso-to-country-code.pipe";
export declare class TuiIsoToCountryCodeModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiIsoToCountryCodeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TuiIsoToCountryCodeModule, [typeof i1.TuiIsoToCountryCodePipe], never, [typeof i1.TuiIsoToCountryCodePipe]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TuiIsoToCountryCodeModule>;
}
