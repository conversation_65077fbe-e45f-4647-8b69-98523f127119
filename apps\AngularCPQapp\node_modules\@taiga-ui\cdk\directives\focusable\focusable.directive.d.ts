import * as i0 from "@angular/core";
/**
 * Abstraction over `tabindex`
 */
export declare class TuiFocusableDirective {
    /**
     * Element can be focused with keyboard
     */
    focusable: boolean;
    get tabIndex(): number;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiFocusableDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiFocusableDirective, "[tuiFocusable]", never, { "focusable": "tuiFocusable"; }, {}, never>;
}
