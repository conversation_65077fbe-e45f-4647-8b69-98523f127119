import { ChangeDetectorRef, ElementRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiControl, TuiContextWithImplicit, TuiFocusableElementAccessor, TuiNativeFocusableElement } from '@taiga-ui/cdk';
import { TuiDecimal, TuiTextfieldController, TuiWithOptionalMinMax } from '@taiga-ui/core';
import { TuiKeySteps } from '@taiga-ui/kit/types';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import * as i0 from "@angular/core";
export declare class TuiInputRangeComponent extends AbstractTuiControl<[number, number]> implements TuiWithOptionalMinMax<number>, TuiFocusableElementAccessor {
    private readonly isMobile;
    private readonly el;
    readonly controller: TuiTextfieldController;
    private readonly inputNumberRefs;
    private readonly rangeRef;
    min: number;
    max: number;
    quantum: number;
    steps: number;
    segments: number;
    keySteps: TuiKeySteps | null;
    leftValueContent: PolymorpheusContent<TuiContextWithImplicit<number>>;
    rightValueContent: PolymorpheusContent<TuiContextWithImplicit<number>>;
    pluralize: Record<string, string> | null;
    leftTextfieldValue: number;
    rightTextfieldValue: number;
    lastActiveSide: 'left' | 'right';
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, isMobile: boolean, el: ElementRef, controller: TuiTextfieldController);
    get leftFocusableElement(): HTMLInputElement | null;
    get rightFocusableElement(): HTMLInputElement | null;
    get nativeFocusableElement(): TuiNativeFocusableElement | null;
    get focused(): boolean;
    get appearance(): string;
    get showLeftValueContent(): boolean;
    get showRightValueContent(): boolean;
    get precision(): number;
    get decimal(): TuiDecimal;
    get computedSteps(): number;
    get step(): number;
    computeKeySteps(keySteps: TuiKeySteps | null, min: number, max: number): TuiKeySteps;
    onActiveZone(active: boolean): void;
    onTextInputFocused(focused: boolean): void;
    changeByStep(event: Event | KeyboardEvent, [leftCoefficient, rightCoefficient]: [number, number]): void;
    onInputLeft(value: number | null): void;
    onInputRight(value: number | null): void;
    onExternalValueUpdate(value: [number, number]): void;
    focusToTextInput(): void;
    onActiveThumbChange(activeThumb: 'left' | 'right'): void;
    writeValue(value: [number, number]): void;
    protected getFallbackValue(): [number, number];
    private safelyUpdateValue;
    private valueGuard;
    private calibrate;
    private updateTextfieldValues;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputRangeComponent, [{ optional: true; self: true; }, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputRangeComponent, "tui-input-range", never, { "min": "min"; "max": "max"; "quantum": "quantum"; "steps": "steps"; "segments": "segments"; "keySteps": "keySteps"; "leftValueContent": "leftValueContent"; "rightValueContent": "rightValueContent"; "pluralize": "pluralize"; }, {}, never, ["*"]>;
}
