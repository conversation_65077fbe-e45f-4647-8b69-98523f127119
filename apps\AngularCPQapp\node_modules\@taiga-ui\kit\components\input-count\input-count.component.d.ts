import { ChangeDetectorRef } from '@angular/core';
import { NgControl } from '@angular/forms';
import { AbstractTuiNullableControl, TuiFocusableElementAccessor } from '@taiga-ui/cdk';
import { TuiNumberFormatSettings, TuiSizeL, TuiSizeS, TuiTextfieldController, TuiWithOptionalMinMax } from '@taiga-ui/core';
import { PolymorpheusContent } from '@tinkoff/ng-polymorpheus';
import { Observable } from 'rxjs';
import { TuiInputCountOptions } from './input-count.options';
import * as i0 from "@angular/core";
/**
 * @deprecated use {@link TuiInputNumberComponent} with [step] instead
 */
export declare class TuiInputCountComponent extends AbstractTuiNullableControl<number> implements TuiWithOptionalMinMax<number>, TuiFocusableElementAccessor {
    private readonly textfieldController;
    readonly minusTexts$: Observable<[string, string]>;
    private readonly isMobile;
    readonly options: TuiInputCountOptions;
    private readonly numberFormat;
    private readonly inputNumber?;
    step: number;
    min: number | null;
    max: number | null;
    hideButtons: boolean;
    /** @deprecated use `tuiTextfieldPrefix` from {@link TuiTextfieldControllerModule} instead */
    prefix: string;
    /** @deprecated use `tuiTextfieldPostfix` from {@link TuiTextfieldControllerModule} instead */
    postfix: string;
    constructor(control: NgControl | null, cdr: ChangeDetectorRef, textfieldController: TuiTextfieldController, minusTexts$: Observable<[string, string]>, isMobile: boolean, options: TuiInputCountOptions, numberFormat: TuiNumberFormatSettings);
    get computedMin(): number;
    get computedMax(): number;
    get buttonsHidden(): boolean;
    get iconUp(): PolymorpheusContent<Record<string, unknown>>;
    get iconDown(): PolymorpheusContent<Record<string, unknown>>;
    get nativeFocusableElement(): HTMLInputElement | null;
    get size(): TuiSizeL | TuiSizeS;
    get focused(): boolean;
    get minusButtonDisabled(): boolean;
    get plusButtonDisabled(): boolean;
    onButtonMouseDown(event: MouseEvent, disabled?: boolean): void;
    onFocused(focused: boolean): void;
    /**
     * @deprecated
     * TODO: drop in v4.0 as unused method
     */
    onInputNumberChange(value: number | null): void;
    /** @deprecated */
    onValueChange(value: string): void;
    decreaseValue(): void;
    increaseValue(): void;
    onKeydown(event: KeyboardEvent): void;
    private safeUpdateValue;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiInputCountComponent, [{ optional: true; self: true; }, null, null, null, null, null, null]>;
    static ɵcmp: i0.ɵɵComponentDeclaration<TuiInputCountComponent, "tui-input-count", never, { "step": "step"; "min": "min"; "max": "max"; "hideButtons": "hideButtons"; "prefix": "prefix"; "postfix": "postfix"; }, {}, never, ["*", "input"]>;
}
