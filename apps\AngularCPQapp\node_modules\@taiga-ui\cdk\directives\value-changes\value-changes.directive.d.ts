import { DoCheck } from '@angular/core';
import { ControlContainer, NgControl } from '@angular/forms';
import { Observable } from 'rxjs';
import * as i0 from "@angular/core";
export declare class TuiValueChangesDirective<T> implements DoCheck {
    private readonly container;
    private readonly control;
    private readonly refresh$;
    readonly tuiValueChanges: Observable<T>;
    constructor(container: ControlContainer | null, control: NgControl | null);
    ngDoCheck(): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<TuiValueChangesDirective<any>, [{ optional: true; }, { optional: true; }]>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TuiValueChangesDirective<any>, "[tuiValueChanges]", never, {}, { "tuiValueChanges": "tuiValueChanges"; }, never>;
}
